# Railway Deployment with NX Monorepo

This guide explains how to deploy your web application to Railway using NX affected commands for efficient CI/CD in a monorepo setup.

## 🚀 Overview

We have implemented an NX-aware Railway deployment workflow that only deploys when the web application or its dependencies are actually affected by changes. This approach is based on [Railway's official NX monorepo guide](https://blog.railway.com/p/nx-railway-with-gh-actions).

## 📁 Workflow Files

### 1. `deploy-web-railway-nx.yml` (Recommended)
- **Smart deployment**: Uses NX affected commands
- **Efficient**: Only deploys when web app or dependencies change
- **Environment-aware**: Supports both production and development
- **Manual control**: Allows force deployment via workflow_dispatch

### 2. `deploy-web-railway.yml` (Legacy)
- **Simple deployment**: Deploys on every push
- **Backup option**: Fallback for when NX detection isn't needed
- **Kept for compatibility**: Can be used for emergency deployments

## 🔧 Setup Requirements

### GitHub Secrets

You need to configure these secrets in your GitHub repository:

```bash
# Railway Service ID for Web App
RAILWAY_SERVICE_ID_WEB=your-web-service-id

# Railway Tokens (Project Tokens)
RAILWAY_TOKEN_WEB_PROD=your-production-project-token
RAILWAY_TOKEN_WEB_DEV=your-development-project-token

# Optional: NX Cloud Token (for better caching)
NX_CLOUD_ACCESS_TOKEN=your-nx-cloud-token
```

### How to Get Railway Service ID

1. Go to your Railway dashboard
2. Navigate to your web service
3. Go to Settings → General
4. Copy the Service ID from the URL or settings page

### How to Get Railway Project Tokens

1. Go to your Railway project dashboard
2. Navigate to Settings → Tokens
3. Create a new Project Token for each environment
4. Copy the tokens and add them to GitHub secrets

## 🎯 How It Works

### NX Affected Detection

The workflow uses NX's affected command to determine if the web app needs deployment:

```bash
# Check which projects are affected
nx show projects --affected --base=BASE_COMMIT --head=HEAD --type=app

# Only deploy if 'web' is in the affected projects list
```

### Deployment Triggers

1. **Automatic (Push)**:
   - `main` branch → Production environment
   - `dev` branch → Development environment
   - Only deploys if web app is affected

2. **Manual (Workflow Dispatch)**:
   - Choose environment: production or development
   - Option to force deploy even if not affected

### Build Process

1. **Setup**: Install Bun and dependencies
2. **Cache**: Restore NX cache and node_modules
3. **Affected Check**: Determine if web app needs deployment
4. **Lint**: Run linting on affected projects
5. **Build**: Build affected projects using NX
6. **Deploy**: Deploy to Railway using CLI

## 🚀 Usage Examples

### Automatic Deployment

```bash
# Push to main - deploys to production if web app affected
git push origin main

# Push to dev - deploys to development if web app affected
git push origin dev
```

### Manual Deployment

1. Go to GitHub Actions tab
2. Select "Deploy Web App to Railway (NX Affected)"
3. Click "Run workflow"
4. Choose:
   - Environment: production or development
   - Force deploy: true (to deploy even if not affected)

### Local Testing

```bash
# Check what's affected locally
nx show projects --affected --base=origin/main --head=HEAD

# Build only affected projects
nx affected --target=build --base=origin/main --head=HEAD

# Lint only affected projects
nx affected --target=lint --base=origin/main --head=HEAD
```

## 🔍 Troubleshooting

### "Prefix not found" Error

1. **Check Railway Service ID**: Ensure `RAILWAY_SERVICE_ID_WEB` is correct
2. **Verify Project Token**: Make sure the Railway token has access to the service
3. **Root Directory**: Set the Railway service root directory to `/apps/web`

### Web App Not Deploying

1. **Check Affected**: Verify if web app is actually affected:
   ```bash
   nx show projects --affected --base=origin/main --head=HEAD
   ```

2. **Force Deploy**: Use manual workflow with `force_deploy=true`

3. **Check Dependencies**: Ensure web app dependencies are properly configured in `project.json`

### Build Failures

1. **Local Build**: Test the build locally:
   ```bash
   nx build web
   ```

2. **Dependencies**: Ensure all dependencies are installed:
   ```bash
   bun install
   ```

3. **Environment Variables**: Check if all required env vars are set in Railway

## 📊 Benefits of NX Approach

### Efficiency
- ✅ Only deploys when necessary
- ✅ Faster CI/CD pipeline
- ✅ Reduced Railway usage costs
- ✅ Less noise in deployment logs

### Reliability
- ✅ Proper dependency tracking
- ✅ Incremental builds
- ✅ Better error isolation
- ✅ Consistent build environment

### Developer Experience
- ✅ Clear deployment reasons
- ✅ Manual override options
- ✅ Comprehensive logging
- ✅ Environment-specific deployments

## 🔄 Migration from Legacy Workflow

If you're migrating from the legacy workflow:

1. **Keep both workflows** initially for safety
2. **Test NX workflow** with development deployments
3. **Monitor affected detection** to ensure it works correctly
4. **Disable legacy workflow** once confident in NX approach
5. **Remove legacy workflow** after successful migration

## 📚 Additional Resources

- [Railway NX Monorepo Guide](https://blog.railway.com/p/nx-railway-with-gh-actions)
- [NX Affected Commands](https://nx.dev/concepts/affected)
- [Railway CLI Documentation](https://docs.railway.app/reference/cli-api)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

## 🤝 Contributing

When making changes to the deployment workflow:

1. Test changes in development environment first
2. Ensure NX affected detection works correctly
3. Update this documentation if needed
4. Test both automatic and manual deployment scenarios
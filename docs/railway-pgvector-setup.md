# Railway pgvector Setup Guide

## 🔍 Problem Analysis

The Railway setup script fails when trying to enable pgvector extension because:

1. **Standard PostgreSQL template doesn't include pgvector** - Railway's default PostgreSQL uses `postgres-ssl` image without pgvector
2. **Incorrect CLI usage** - `railway run --service "Postgres" -- psql` fails because `psql` isn't available in the CLI environment
3. **Extension not pre-installed** - pgvector requires specific PostgreSQL images with the extension compiled

## ✅ Solutions

### Option 1: Use Railway's pgvector Template (Recommended)

Railway provides dedicated pgvector templates with the extension pre-installed:

#### **PostgreSQL 16 + pgvector**
- Template URL: https://railway.com/deploy/3jJFCA
- Image: `pgvector/pgvector:pg16`
- Status: Stable, widely used (737+ deployments)

#### **PostgreSQL 17 + pgvector** 
- Template URL: https://railway.com/deploy/qcuy_M
- Image: `pgvector/pgvector:pg17`
- Status: Newer, fewer deployments (40+ deployments)

### Option 2: Replace Existing PostgreSQL Service

If you already have a standard PostgreSQL service:

1. **Backup your data** (if any exists)
2. **Remove the existing PostgreSQL service**
3. **Deploy pgvector template**
4. **Restore data** (if needed)

### Option 3: Manual pgvector Setup (Advanced)

For existing PostgreSQL services, you can try to manually install pgvector, but this requires container modification and is not recommended.

## 🚀 Implementation Steps

### Step 1: Deploy pgvector Template

1. Go to Railway dashboard
2. Create new project or use existing
3. Click "Deploy Template"
4. Use one of the pgvector template URLs above
5. Deploy and wait for completion

### Step 2: Connect and Setup Database

#### Install PostgreSQL Client (if needed)

```bash
# macOS
brew install postgresql

# Ubuntu/Debian
sudo apt-get install postgresql-client

# CentOS/RHEL
sudo yum install postgresql
```

#### Connect to Database

```bash
# Connect using Railway CLI
railway connect [postgres-service-name]

# Or connect using connection string
psql $DATABASE_URL
```

#### Enable pgvector Extension

```sql
-- Enable the extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Verify installation
SELECT * FROM pg_extension WHERE extname = 'vector';
```

### Step 3: Create Embeddings Schema

```sql
-- Create embeddings table
CREATE TABLE IF NOT EXISTS learning_content_embeddings (
  id SERIAL PRIMARY KEY,
  content_id VARCHAR(255) NOT NULL,
  content_type VARCHAR(100) NOT NULL,
  title TEXT,
  content TEXT,
  embedding vector(1024) NOT NULL,  -- Voyage AI dimension
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_embeddings_hnsw 
ON learning_content_embeddings USING hnsw (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS idx_embeddings_content_id 
ON learning_content_embeddings (content_id);

CREATE INDEX IF NOT EXISTS idx_embeddings_content_type 
ON learning_content_embeddings (content_type);

CREATE INDEX IF NOT EXISTS idx_embeddings_metadata 
ON learning_content_embeddings USING gin (metadata);

-- Unique constraint to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_embeddings_unique 
ON learning_content_embeddings (content_id, content_type);
```

### Step 4: Test the Setup

```sql
-- Test vector operations
INSERT INTO learning_content_embeddings 
(content_id, content_type, title, content, embedding, metadata) 
VALUES ('test-1', 'document', 'Test Document', 'This is a test', 
        array_fill(0.1, ARRAY[1024])::vector, '{"test": true}');

-- Test similarity search
SELECT content_id, title, 
       embedding <-> array_fill(0.1, ARRAY[1024])::vector as distance 
FROM learning_content_embeddings 
ORDER BY embedding <-> array_fill(0.1, ARRAY[1024])::vector 
LIMIT 5;

-- Clean up
DELETE FROM learning_content_embeddings WHERE content_id = 'test-1';
```

## 🔧 Updated Script Integration

The Railway setup script has been updated to:

1. **Detect pgvector availability** - Check if current PostgreSQL supports pgvector
2. **Provide clear instructions** - Show manual setup steps when automated setup fails
3. **Recommend pgvector templates** - Guide users to proper templates
4. **Handle graceful fallbacks** - Continue script execution even if pgvector setup fails

## 📊 Vector Operations Reference

### Distance Metrics

```sql
-- L2 distance (Euclidean)
SELECT * FROM embeddings ORDER BY embedding <-> '[0.1,0.2,0.3]' LIMIT 10;

-- Cosine distance  
SELECT * FROM embeddings ORDER BY embedding <=> '[0.1,0.2,0.3]' LIMIT 10;

-- Inner product
SELECT * FROM embeddings ORDER BY embedding <#> '[0.1,0.2,0.3]' LIMIT 10;
```

### Index Types

```sql
-- HNSW index (approximate, faster)
CREATE INDEX ON embeddings USING hnsw (embedding vector_cosine_ops);

-- IVFFlat index (exact, slower)
CREATE INDEX ON embeddings USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
```

## 🔗 Resources

- [pgvector GitHub](https://github.com/pgvector/pgvector)
- [Railway pgvector Template](https://railway.com/deploy/3jJFCA)
- [Railway PostgreSQL Docs](https://docs.railway.com/guides/postgresql)
- [pgvector Documentation](https://github.com/pgvector/pgvector#pgvector)

## 💡 Best Practices

1. **Use appropriate vector dimensions** - 1024 for Voyage AI, 1536 for OpenAI
2. **Choose right index type** - HNSW for speed, IVFFlat for accuracy
3. **Monitor performance** - Vector operations are memory-intensive
4. **Regular backups** - Use Railway's backup features for production
5. **Connection pooling** - Important for concurrent vector operations

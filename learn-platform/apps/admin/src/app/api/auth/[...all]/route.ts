/**
 * Better-auth API route handler for Next.js Admin App
 *
 * ⚠️  DISABLED: These routes are commented out as part of authentication standardization.
 * The admin app now uses the centralized Cloudflare Workers API (localhost:8787) for authentication.
 *
 * These routes are kept for potential fallback purposes but are not currently used.
 * To re-enable local authentication, uncomment the exports below and update the auth client
 * in src/lib/auth-client.ts to use relative URLs instead of the API service.
 *
 * Current architecture: Admin App → Cloudflare Workers API → Database
 * Previous architecture: Admin App → Local Next.js API Routes → Database
 */

import { auth } from '@learn-platform/auth';

// Disabled: Admin app now uses centralized API service
// export async function GET(request: Request) {
//   return auth.handler(request);
// }

// export async function POST(request: Request) {
//   return auth.handler(request);
// }

/**
 * Fallback routes (commented out)
 *
 * If you need to re-enable local authentication for the admin app:
 * 1. Uncomment the exports above
 * 2. Update apps/admin/src/lib/auth-client.ts getBaseUrl() to return ''
 * 3. Update apps/admin/.env.local BETTER_AUTH_URL to http://localhost:3001
 * 4. Restart the admin app
 */

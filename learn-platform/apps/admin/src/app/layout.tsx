import './global.css';
import { TRPCProvider } from '../lib/providers';
import { AuthProvider } from '../components/auth/auth-provider';
import { ThemeProvider } from '@learn-platform/theme';

export const metadata = {
  title: 'Learning Platform - Admin',
  description: 'Admin panel for the learning platform',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider>
          <TRPCProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </TRPCProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

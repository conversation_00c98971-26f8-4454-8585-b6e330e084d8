'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { api } from '../../../lib/trpc';
import { X, Upload, FileText, AlertCircle, CheckCircle, Download } from 'lucide-react';
import { toast } from 'sonner';

interface CSVUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  focusAreas: Array<{ id: string; name: string }>;
  themes: Array<{ theme: { id: string; name: string }; focusArea: { id: string; name: string } }>;
}

export function CSVUploadModal({ isOpen, onClose, onSuccess, focusAreas, themes }: CSVUploadModalProps) {
  const [selectedFocusArea, setSelectedFocusArea] = useState('');
  const [selectedTheme, setSelectedTheme] = useState('');
  const [csvContent, setCsvContent] = useState('');
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    createdCount: number;
    errors?: string[];
  } | null>(null);

  const bulkUploadMutation = api.interests.bulkUploadTopics.useMutation({
    onSuccess: (data) => {
      setUploadResult(data);
      if (data.success) {
        toast.success(`Successfully uploaded ${data.createdCount} topics`);
        onSuccess();
      }
    },
    onError: (error) => {
      toast.error(`Upload failed: ${error.message}`);
    },
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      toast.error('Please select a CSV file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvContent(content);
    };
    reader.readAsText(file);
  };

  const handleUpload = async () => {
    if (!selectedFocusArea || !selectedTheme || !csvContent) {
      toast.error('Please select focus area, theme, and upload a CSV file');
      return;
    }

    try {
      await bulkUploadMutation.mutateAsync({
        csvData: csvContent,
        focusAreaId: selectedFocusArea,
        themeId: selectedTheme,
      });
    } catch (error) {
      // Error handling is done in mutation onError
    }
  };

  const handleClose = () => {
    setSelectedFocusArea('');
    setSelectedTheme('');
    setCsvContent('');
    setUploadResult(null);
    onClose();
  };

  const downloadTemplate = () => {
    const template = `name,description,difficulty,keywords,estimatedtime
Computer Science,Fundamental computer science concepts and programming,intermediate,programming;algorithms;data structures,40
Mathematics,Mathematical foundations and problem-solving,intermediate,algebra;calculus;geometry,35
Physics,Physical laws and natural phenomena,intermediate,mechanics;thermodynamics;electromagnetism,45`;

    const blob = new Blob([template], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'topics-template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const filteredThemes = themes.filter(t => !selectedFocusArea || t.focusArea.id === selectedFocusArea);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Bulk Upload Topics</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <FileText className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
              <div>
                <h3 className="font-medium text-blue-900">CSV Format Instructions</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Upload a CSV file with the following columns:
                </p>
                <ul className="text-sm text-blue-700 mt-2 list-disc list-inside">
                  <li><strong>name</strong> (required): Topic name</li>
                  <li><strong>description</strong> (required): Topic description</li>
                  <li><strong>difficulty</strong> (optional): beginner, intermediate, or advanced</li>
                  <li><strong>keywords</strong> (optional): Semicolon-separated keywords</li>
                  <li><strong>estimatedtime</strong> (optional): Estimated learning time in hours</li>
                </ul>
                <div className="mt-3">
                  <Button
                    onClick={downloadTemplate}
                    variant="outline"
                    size="sm"
                    className="text-blue-600 border-blue-300 hover:bg-blue-50"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Template
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Focus Area Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Focus Area *
            </label>
            <select
              value={selectedFocusArea}
              onChange={(e) => {
                setSelectedFocusArea(e.target.value);
                setSelectedTheme(''); // Reset theme when focus area changes
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select a focus area</option>
              {focusAreas.map((fa) => (
                <option key={fa.id} value={fa.id}>{fa.name}</option>
              ))}
            </select>
          </div>

          {/* Theme Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Theme *
            </label>
            <select
              value={selectedTheme}
              onChange={(e) => setSelectedTheme(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={!selectedFocusArea}
            >
              <option value="">Select a theme</option>
              {filteredThemes.map((item) => (
                <option key={item.theme.id} value={item.theme.id}>{item.theme.name}</option>
              ))}
            </select>
          </div>

          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CSV File *
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-2">
                Click to upload or drag and drop your CSV file
              </p>
              <input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="hidden"
                id="csv-upload"
              />
              <label
                htmlFor="csv-upload"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
              >
                Choose File
              </label>
              {csvContent && (
                <p className="text-sm text-green-600 mt-2">
                  ✓ CSV file loaded ({csvContent.split('\n').length - 1} rows)
                </p>
              )}
            </div>
          </div>

          {/* Upload Result */}
          {uploadResult && (
            <div className={`border rounded-lg p-4 ${
              uploadResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-start">
                {uploadResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-3" />
                )}
                <div>
                  <h3 className={`font-medium ${
                    uploadResult.success ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {uploadResult.success ? 'Upload Successful' : 'Upload Failed'}
                  </h3>
                  <p className={`text-sm mt-1 ${
                    uploadResult.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {uploadResult.success 
                      ? `Successfully created ${uploadResult.createdCount} topics`
                      : 'Some errors occurred during upload'
                    }
                  </p>
                  {uploadResult.errors && uploadResult.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-red-700">Errors:</p>
                      <ul className="text-sm text-red-600 mt-1 list-disc list-inside">
                        {uploadResult.errors.slice(0, 5).map((error, idx) => (
                          <li key={idx}>{error}</li>
                        ))}
                        {uploadResult.errors.length > 5 && (
                          <li>... and {uploadResult.errors.length - 5} more errors</li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <Button
            onClick={handleClose}
            variant="outline"
          >
            {uploadResult?.success ? 'Close' : 'Cancel'}
          </Button>
          {!uploadResult?.success && (
            <Button
              onClick={handleUpload}
              disabled={!selectedFocusArea || !selectedTheme || !csvContent || bulkUploadMutation.isPending}
            >
              {bulkUploadMutation.isPending ? 'Uploading...' : 'Upload Topics'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

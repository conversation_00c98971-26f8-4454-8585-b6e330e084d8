'use client';

import React, { useState } from 'react';
import { ProtectedRoute } from '../../components/auth/protected-route';
import { <PERSON><PERSON>, <PERSON>, CardHeader, CardTitle, CardContent } from '@learn-platform/shared-ui';
import { AdminLayout } from '../../lib/components/layout';
import { api } from '../../lib/trpc';
import {
  Brain,
  Plus,
  Search,
  Filter,
  Upload,
  Edit,
  Trash2,
  FolderOpen,
  Tag,
  BookOpen,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import { toast } from 'sonner';
import { CSVUploadModal } from './components/CSVUploadModal';

type TabType = 'focus-areas' | 'themes' | 'topics';

export default function LearningInterestsPage() {
  const [activeTab, setActiveTab] = useState<TabType>('focus-areas');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedFocusArea, setSelectedFocusArea] = useState<string>('');
  const [selectedTheme, setSelectedTheme] = useState<string>('');

  // Fetch data
  const { data: focusAreasData, isLoading: loadingFocusAreas, refetch: refetchFocusAreas } =
    api.interests.getAllFocusAreas.useQuery();
  const { data: themesData, isLoading: loadingThemes, refetch: refetchThemes } =
    api.interests.getAllThemes.useQuery();
  const { data: topicsData, isLoading: loadingTopics, refetch: refetchTopics } =
    api.interests.getAllTopics.useQuery({
      page: 1,
      limit: 50,
      search: searchQuery || undefined,
      focusAreaId: selectedFocusArea || undefined,
      themeId: selectedTheme || undefined,
    });

  const focusAreas = focusAreasData?.focusAreas || [];
  const themes = themesData?.themes || [];
  const topics = topicsData?.topics || [];

  // Mutations
  const deleteFocusAreaMutation = api.interests.deleteFocusArea.useMutation({
    onSuccess: () => {
      toast.success('Focus area deleted successfully');
      refetchFocusAreas();
      refetchThemes();
      refetchTopics();
    },
    onError: (error) => {
      toast.error(`Failed to delete focus area: ${error.message}`);
    },
  });

  const deleteThemeMutation = api.interests.deleteTheme.useMutation({
    onSuccess: () => {
      toast.success('Theme deleted successfully');
      refetchThemes();
      refetchTopics();
    },
    onError: (error) => {
      toast.error(`Failed to delete theme: ${error.message}`);
    },
  });

  const deleteTopicMutation = api.interests.deleteTopic.useMutation({
    onSuccess: () => {
      toast.success('Topic deleted successfully');
      refetchTopics();
    },
    onError: (error) => {
      toast.error(`Failed to delete topic: ${error.message}`);
    },
  });

  const handleDelete = async (type: string, id: string, name: string) => {
    if (!confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      switch (type) {
        case 'focus-area':
          await deleteFocusAreaMutation.mutateAsync({ id });
          break;
        case 'theme':
          await deleteThemeMutation.mutateAsync({ id });
          break;
        case 'topic':
          await deleteTopicMutation.mutateAsync({ id });
          break;
      }
    } catch (error) {
      // Error handling is done in mutation onError callbacks
    }
  };

  const renderFocusAreas = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Focus Areas ({focusAreas.length})</span>
          <Button onClick={() => setShowCreateModal(true)} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Focus Area
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loadingFocusAreas ? (
          <div className="text-center py-8">Loading focus areas...</div>
        ) : focusAreas.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No focus areas found. Create your first focus area to get started.
          </div>
        ) : (
          <div className="space-y-4">
            {focusAreas.map((focusArea: any) => (
              <div key={focusArea.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{focusArea.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{focusArea.description}</p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <span>Order: {focusArea.order}</span>
                      <span className="mx-2">•</span>
                      <span>Created: {new Date(focusArea.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete('focus-area', focusArea.id, focusArea.name)}
                      disabled={deleteFocusAreaMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderThemes = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Themes ({themes.length})</span>
          <Button onClick={() => setShowCreateModal(true)} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Theme
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loadingThemes ? (
          <div className="text-center py-8">Loading themes...</div>
        ) : themes.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No themes found. Create focus areas first, then add themes.
          </div>
        ) : (
          <div className="space-y-4">
            {themes.map((item: any) => (
              <div key={item.theme.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium text-gray-900">{item.theme.name}</h3>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        {item.focusArea.name}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{item.theme.description}</p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <span>Order: {item.theme.order}</span>
                      <span className="mx-2">•</span>
                      <span>Created: {new Date(item.theme.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete('theme', item.theme.id, item.theme.name)}
                      disabled={deleteThemeMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderTopics = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Topics ({topics.length})</span>
          <div className="flex items-center space-x-2">
            <Button onClick={() => setShowUploadModal(true)} variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Bulk Upload
            </Button>
            <Button onClick={() => setShowCreateModal(true)} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Topic
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={selectedFocusArea}
            onChange={(e) => setSelectedFocusArea(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Focus Areas</option>
            {focusAreas.map((fa: any) => (
              <option key={fa.id} value={fa.id}>{fa.name}</option>
            ))}
          </select>
          <select
            value={selectedTheme}
            onChange={(e) => setSelectedTheme(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Themes</option>
            {themes
              .filter((t: any) => !selectedFocusArea || t.focusArea.id === selectedFocusArea)
              .map((item: any) => (
                <option key={item.theme.id} value={item.theme.id}>{item.theme.name}</option>
              ))}
          </select>
        </div>

        {loadingTopics ? (
          <div className="text-center py-8">Loading topics...</div>
        ) : topics.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No topics found. Create themes first, then add topics.
          </div>
        ) : (
          <div className="space-y-4">
            {topics.map((item: any) => (
              <div key={item.topic.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-medium text-gray-900">{item.topic.name}</h3>
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        {item.topic.metadata?.difficulty || 'beginner'}
                      </span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                        {item.theme.name}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{item.topic.description}</p>
                    <div className="flex items-center text-xs text-gray-500">
                      <span>Order: {item.topic.order}</span>
                      {item.topic.metadata?.estimatedLearningTime && (
                        <>
                          <span className="mx-2">•</span>
                          <span>{item.topic.metadata.estimatedLearningTime}h</span>
                        </>
                      )}
                      <span className="mx-2">•</span>
                      <span>Created: {new Date(item.topic.createdAt).toLocaleDateString()}</span>
                    </div>
                    {item.topic.metadata?.keywords && item.topic.metadata.keywords.length > 0 && (
                      <div className="flex items-center mt-2">
                        <Tag className="h-3 w-3 text-gray-400 mr-1" />
                        <div className="flex flex-wrap gap-1">
                          {item.topic.metadata.keywords.slice(0, 3).map((keyword: any, idx: number) => (
                            <span key={idx} className="px-1 py-0.5 bg-gray-100 text-gray-600 text-xs rounded">
                              {keyword}
                            </span>
                          ))}
                          {item.topic.metadata.keywords.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{item.topic.metadata.keywords.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete('topic', item.topic.id, item.topic.name)}
                      disabled={deleteTopicMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Brain className="h-8 w-8 mr-3 text-purple-600" />
                Learning Interests Management
              </h1>
              <p className="text-gray-600">Manage focus areas, themes, and topics for the learning interests system</p>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <FolderOpen className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Focus Areas</p>
                    <p className="text-2xl font-bold text-gray-900">{focusAreas.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Tag className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Themes</p>
                    <p className="text-2xl font-bold text-gray-900">{themes.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <BookOpen className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Topics</p>
                    <p className="text-2xl font-bold text-gray-900">{topicsData?.pagination?.total || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'focus-areas', label: 'Focus Areas', icon: FolderOpen },
                { id: 'themes', label: 'Themes', icon: Tag },
                { id: 'topics', label: 'Topics', icon: BookOpen },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as TabType)}
                    className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div>
            {activeTab === 'focus-areas' && renderFocusAreas()}
            {activeTab === 'themes' && renderThemes()}
            {activeTab === 'topics' && renderTopics()}
          </div>
        </div>

        {/* CSV Upload Modal */}
        <CSVUploadModal
          isOpen={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          onSuccess={() => {
            refetchTopics();
            setShowUploadModal(false);
          }}
          focusAreas={focusAreas}
          themes={themes}
        />
      </AdminLayout>
    </ProtectedRoute>
  );
}

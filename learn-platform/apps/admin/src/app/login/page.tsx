'use client';

import { useRouter } from 'next/navigation';
import { PublicOnlyRoute } from '../../components/auth/protected-route';
import { LoginForm } from '../../components/auth/login-form';
import Link from 'next/link';

export default function AdminLoginPage() {
  const router = useRouter();

  const handleLoginSuccess = () => {
    // Redirect to admin dashboard after successful login
    router.push('/dashboard');
  };

  return (
    <PublicOnlyRoute>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              🔐 Admin Login
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Sign in to access the admin dashboard
            </p>
          </div>

          <LoginForm
            onSuccess={handleLoginSuccess}
            onError={(error) => console.error('Admin login error:', error)}
          />

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Need help?{' '}
              <Link
                href="/contact"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Contact support
              </Link>
            </p>
          </div>
        </div>
      </div>
    </PublicOnlyRoute>
  );
}

'use client';

import React from 'react';
import { <PERSON>, <PERSON>O<PERSON>, Zap } from 'lucide-react';
import { MultiStepExplain } from '../../lib/components/templates';
import { StepConfig } from '../../lib/components/templates/types';

export default function TestMultiStepPage() {
  const testSteps: StepConfig[] = [
    {
      title: "Simple Paragraph",
      icon: <Brain className="w-8 h-8 text-blue-500" />,
      type: 'paragraph',
      data: "This is a simple paragraph to test the MultiStepExplain component."
    },
    {
      title: "Info Box Example",
      icon: <BookOpen className="w-8 h-8 text-green-500" />,
      type: 'infoBox',
      data: {
        heading: "Key Information",
        lines: [
          "This is an info box with multiple lines",
          "Each line is displayed separately",
          "Perfect for highlighting important points"
        ]
      }
    },
    {
      title: "Bullet List",
      icon: <Zap className="w-8 h-8 text-purple-500" />,
      type: 'bulletList',
      data: [
        "First bullet point",
        "Second bullet point",
        "Third bullet point"
      ]
    },
    {
      title: "Numbered Steps",
      icon: <Brain className="w-8 h-8 text-orange-500" />,
      type: 'numberedList',
      data: [
        "First step in the process",
        "Second step to follow",
        "Final step to complete"
      ]
    },
    {
      title: "Grid Layout",
      icon: <BookOpen className="w-8 h-8 text-red-500" />,
      type: 'grid',
      data: [
        {
          title: "Feature 1",
          content: "Description of the first feature"
        },
        {
          title: "Feature 2", 
          content: "Description of the second feature"
        }
      ]
    },
    {
      title: "Key-Value Pairs",
      icon: <Zap className="w-8 h-8 text-indigo-500" />,
      type: 'keyValueGrid',
      data: [
        {
          key: "Term 1",
          value: "Definition of the first term"
        },
        {
          key: "Term 2",
          value: "Definition of the second term"
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            MultiStepExplain Test Page
          </h1>
          <p className="text-xl text-gray-600">
            Testing the new template component
          </p>
        </div>
        
        <MultiStepExplain steps={testSteps} />
      </div>
    </div>
  );
}

'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  CardContent,
  Input
} from '@learn-platform/shared-ui';
import { useState } from 'react';
import Link from 'next/link';

export default function TestUIPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleSubmit = () => {
    alert(`Form submitted with: ${JSON.stringify(formData, null, 2)}`);
  };

  const handleReset = () => {
    setFormData({ name: '', email: '', message: '' });
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container-wide">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Admin App - Shared UI Components Test
          </h1>
          <p className="text-muted-foreground text-lg">
            Testing TailwindCSS integration with shared UI components from @learn-platform/shared-ui
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* Form Example */}
          <Card className="p-6">
            <CardHeader>
              <CardTitle>Admin Form Example</CardTitle>
              <CardDescription>
                Testing form components with validation styling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <label htmlFor="admin-name" className="text-sm font-medium">
                  Full Name *
                </label>
                <Input
                  id="admin-name"
                  type="text"
                  placeholder="Enter full name"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  className={formData.name ? 'border-success-500' : ''}
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="admin-email" className="text-sm font-medium">
                  Email Address *
                </label>
                <Input
                  id="admin-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  className={formData.email ? 'border-success-500' : ''}
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="admin-message" className="text-sm font-medium">
                  Message
                </label>
                <Input
                  id="admin-message"
                  type="text"
                  placeholder="Optional message"
                  value={formData.message}
                  onChange={handleInputChange('message')}
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handleSubmit}
                  disabled={!formData.name || !formData.email}
                  className="flex-1"
                >
                  Submit Form
                </Button>
                <Button
                  onClick={handleReset}
                  variant="outline"
                >
                  Reset
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Admin Actions */}
          <Card className="p-6">
            <CardHeader>
              <CardTitle>Admin Actions</CardTitle>
              <CardDescription>
                Testing different button states and variants for admin interface
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-3">
                <Button variant="default" size="sm">
                  Create User
                </Button>
                <Button variant="secondary" size="sm">
                  Edit Settings
                </Button>
                <Button variant="destructive" size="sm">
                  Delete Item
                </Button>
                <Button variant="outline" size="sm">
                  Export Data
                </Button>
              </div>

              <div className="space-y-3">
                <Button variant="ghost" className="w-full justify-start">
                  📊 View Analytics
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  👥 Manage Users
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  ⚙️ System Settings
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  📝 View Logs
                </Button>
              </div>

              <div className="pt-4 border-t">
                <Button variant="link" className="w-full">
                  Advanced Configuration →
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Status Cards */}
        <div className="grid gap-4 md:grid-cols-4 mt-8">
          <Card className="p-4 bg-gradient-to-br from-brand-50 to-brand-100 border-brand-200">
            <CardContent className="p-0">
              <div className="text-2xl font-bold text-brand-700">1,234</div>
              <div className="text-sm text-brand-600">Total Users</div>
            </CardContent>
          </Card>

          <Card className="p-4 bg-gradient-to-br from-success-50 to-success-100 border-success-200">
            <CardContent className="p-0">
              <div className="text-2xl font-bold text-success-700">98.5%</div>
              <div className="text-sm text-success-600">Uptime</div>
            </CardContent>
          </Card>

          <Card className="p-4 bg-gradient-to-br from-warning-50 to-warning-100 border-warning-200">
            <CardContent className="p-0">
              <div className="text-2xl font-bold text-warning-700">23</div>
              <div className="text-sm text-warning-600">Pending</div>
            </CardContent>
          </Card>

          <Card className="p-4 bg-gradient-to-br from-error-50 to-error-100 border-error-200">
            <CardContent className="p-0">
              <div className="text-2xl font-bold text-error-700">2</div>
              <div className="text-sm text-error-600">Critical Issues</div>
            </CardContent>
          </Card>
        </div>

        {/* Form Preview */}
        {(formData.name || formData.email || formData.message) && (
          <Card className="mt-8 p-6">
            <CardHeader>
              <CardTitle>Form Preview</CardTitle>
              <CardDescription>
                Live preview of form data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-lg">
                <pre className="text-sm">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="mt-8 flex justify-center">
          <Button asChild variant="outline">
            <Link href="/">← Back to Home</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}

'use client';

import { ProtectedRoute } from '../../components/auth/protected-route';
import { useAuth } from '../../components/auth/auth-provider';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardContent } from '@learn-platform/shared-ui';
import { AdminLayout } from '../../lib/components/layout';

export default function AdminDashboard() {
  const { user } = useAuth();

  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back, {user?.name}</p>
          </div>

          {/* Welcome Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-green-800">
                ✅ Authentication Successful
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  <strong>User ID:</strong> {user?.id}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Email:</strong> {user?.email}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Name:</strong> {user?.name}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Email Verified:</strong> {user?.emailVerified ? 'Yes' : 'No'}
                </p>
              </div>
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  🎉 <strong>Centralized Authentication Working!</strong>
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  This admin app is successfully using the centralized Cloudflare Workers API
                  (localhost:8787) for authentication. Session data is shared with the web app.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Dashboard Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Users Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    👥 User Management
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Manage user accounts and permissions
                  </p>
                  <Button variant="outline" className="w-full">
                    View Users
                  </Button>
                </CardContent>
              </Card>

              {/* Content Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    📚 Content Management
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Manage courses and learning materials
                  </p>
                  <Button variant="outline" className="w-full">
                    Manage Content
                  </Button>
                </CardContent>
              </Card>

              {/* Component Builder */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    🎨 Component Builder
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Create and design UI components visually
                  </p>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => window.location.href = '/builder'}
                  >
                    Open Builder
                  </Button>
                </CardContent>
              </Card>

              {/* Analytics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    📊 Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    View platform analytics and reports
                  </p>
                  <Button variant="outline" className="w-full">
                    View Analytics
                  </Button>
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    ⚙️ Settings
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Configure platform settings
                  </p>
                  <Button variant="outline" className="w-full">
                    Settings
                  </Button>
                </CardContent>
              </Card>

              {/* Authentication Test */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    🔐 Auth Test
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Test authentication features
                  </p>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      className="w-full text-xs"
                      onClick={() => window.open('http://localhost:3000', '_blank')}
                    >
                      Open Web App (Test Session Sharing)
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full text-xs"
                      onClick={() => window.open('http://localhost:8787/api/auth/session', '_blank')}
                    >
                      View API Session
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* System Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    🟢 System Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Admin App:</span>
                      <span className="text-green-600">✅ Running</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>API Service:</span>
                      <span className="text-green-600">✅ Connected</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Database:</span>
                      <span className="text-green-600">✅ Connected</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Auth:</span>
                      <span className="text-green-600">✅ Authenticated</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
          </div>

          {/* Footer Info */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              Admin Dashboard • Powered by Centralized Authentication •
              API: localhost:8787 • Session Shared with Web App
            </p>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

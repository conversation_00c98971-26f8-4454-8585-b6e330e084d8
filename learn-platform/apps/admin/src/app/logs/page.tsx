'use client';

import { ProtectedRoute } from '../../components/auth/protected-route';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardContent } from '@learn-platform/shared-ui';
import { AdminLayout } from '../../lib/components/layout';
import { FileText, Download, Filter, Search, RefreshCw } from 'lucide-react';

export default function LogsPage() {
  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <FileText className="h-8 w-8 mr-3 text-blue-600" />
                System Logs
              </h1>
              <p className="text-gray-600">Monitor system activity and audit trails</p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" className="flex items-center">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" className="flex items-center">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Filters and Search */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search logs..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div className="flex gap-2">
                  <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option>All Levels</option>
                    <option>Error</option>
                    <option>Warning</option>
                    <option>Info</option>
                    <option>Debug</option>
                  </select>
                  <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option>Last 24 hours</option>
                    <option>Last 7 days</option>
                    <option>Last 30 days</option>
                    <option>Custom range</option>
                  </select>
                  <Button variant="outline" className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    More Filters
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Log Entries */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Log Entries</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Sample log entries */}
                <div className="flex items-start space-x-4 p-3 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      ERROR
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 font-medium">
                      Database connection failed
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Failed to connect to PostgreSQL database at localhost:5432
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      2024-01-15 14:32:15 • API Service • Request ID: req_123456
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-3 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      WARNING
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 font-medium">
                      High memory usage detected
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Memory usage exceeded 80% threshold (85.2% used)
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      2024-01-15 14:30:42 • Admin App • Process ID: 12345
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-3 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      INFO
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 font-medium">
                      User authentication successful
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      User <EMAIL> logged in successfully
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      2024-01-15 14:28:33 • Auth Service • User ID: user_789
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-3 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      SUCCESS
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 font-medium">
                      Database backup completed
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Scheduled backup completed successfully (2.3GB)
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      2024-01-15 14:25:00 • Backup Service • Backup ID: backup_456
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-3 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      DEBUG
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 font-medium">
                      API request processed
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      GET /api/users - 200 OK (142ms)
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      2024-01-15 14:22:18 • API Service • IP: *************
                    </p>
                  </div>
                </div>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-500">
                  Showing 1-5 of 1,234 log entries
                </p>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" disabled>
                    Previous
                  </Button>
                  <Button variant="outline" size="sm">
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Log Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <FileText className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Errors (24h)</p>
                    <p className="text-2xl font-bold text-gray-900">12</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <FileText className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Warnings (24h)</p>
                    <p className="text-2xl font-bold text-gray-900">45</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Info (24h)</p>
                    <p className="text-2xl font-bold text-gray-900">1,187</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <FileText className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total (24h)</p>
                    <p className="text-2xl font-bold text-gray-900">1,244</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

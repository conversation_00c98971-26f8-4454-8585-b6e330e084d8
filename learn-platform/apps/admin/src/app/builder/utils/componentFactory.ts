/**
 * Component factory utilities for the Component Builder
 * Handles component creation, default props, and palette definitions
 */

import { ComponentType, PaletteItem, ComponentProps, Size } from '../types';
import { getIconByName } from './iconMapping';

/**
 * Default sizes for each component type
 */
export const DEFAULT_COMPONENT_SIZES: Record<ComponentType, Size> = {
  button: { width: 120, height: 40 },
  input: { width: 200, height: 40 },
  text: { width: 150, height: 24 },
  container: { width: 300, height: 200 },
  multiStepExplain: { width: 600, height: 400 },
};

/**
 * Default props for each component type
 */
export const DEFAULT_COMPONENT_PROPS: Record<ComponentType, ComponentProps> = {
  button: {
    variant: 'default',
    size: 'default',
    children: 'Button',
  },
  input: {
    type: 'text',
    placeholder: 'Enter text...',
  },
  text: {
    text: 'Sample text',
    tag: 'p',
  },
  container: {
    padding: 16,
    backgroundColor: '#f3f4f6',
  },
  multiStepExplain: {
    steps: [
      {
        title: 'Welcome',
        icon: 'Brain', // Store icon name as string
        type: 'paragraph',
        data: 'Welcome to your multi-step explanation. Click the properties panel to customize this content.'
      },
      {
        title: 'Getting Started',
        icon: 'BookOpen', // Store icon name as string
        type: 'infoBox',
        data: {
          heading: 'Quick Start',
          lines: [
            'Add more steps using the properties panel',
            'Choose different content types for each step',
            'Customize icons and titles'
          ]
        }
      }
    ]
  },
};

/**
 * Component palette items with metadata
 */
export const PALETTE_ITEMS: PaletteItem[] = [
  {
    type: 'button',
    label: 'Button',
    icon: '🔘',
    defaultProps: DEFAULT_COMPONENT_PROPS.button,
    defaultSize: DEFAULT_COMPONENT_SIZES.button,
  },
  {
    type: 'input',
    label: 'Input',
    icon: '📝',
    defaultProps: DEFAULT_COMPONENT_PROPS.input,
    defaultSize: DEFAULT_COMPONENT_SIZES.input,
  },
  {
    type: 'text',
    label: 'Text',
    icon: '📄',
    defaultProps: DEFAULT_COMPONENT_PROPS.text,
    defaultSize: DEFAULT_COMPONENT_SIZES.text,
  },
  {
    type: 'container',
    label: 'Container',
    icon: '📦',
    defaultProps: DEFAULT_COMPONENT_PROPS.container,
    defaultSize: DEFAULT_COMPONENT_SIZES.container,
  },
  {
    type: 'multiStepExplain',
    label: 'Multi-Step Explain',
    icon: '📚',
    defaultProps: DEFAULT_COMPONENT_PROPS.multiStepExplain,
    defaultSize: DEFAULT_COMPONENT_SIZES.multiStepExplain,
  },
];

/**
 * Get default props for a component type
 */
export const getDefaultProps = (type: ComponentType): ComponentProps => {
  return { ...DEFAULT_COMPONENT_PROPS[type] };
};

/**
 * Get default size for a component type
 */
export const getDefaultSize = (type: ComponentType): Size => {
  return { ...DEFAULT_COMPONENT_SIZES[type] };
};

/**
 * Get palette item by component type
 */
export const getPaletteItem = (type: ComponentType): PaletteItem | undefined => {
  return PALETTE_ITEMS.find(item => item.type === type);
};

/**
 * Validate component props based on type
 */
export const validateComponentProps = (type: ComponentType, props: ComponentProps): boolean => {
  switch (type) {
    case 'button':
      return true; // Buttons are flexible with props

    case 'input':
      return typeof props.type === 'string' || props.type === undefined;

    case 'text':
      return typeof props.text === 'string' || props.text === undefined;

    case 'container':
      return typeof props.padding === 'number' || props.padding === undefined;

    case 'multiStepExplain':
      return Array.isArray(props.steps) || props.steps === undefined;

    default:
      return false;
  }
};

/**
 * Merge props with defaults for a component type
 */
export const mergeWithDefaults = (type: ComponentType, props: Partial<ComponentProps>): ComponentProps => {
  const defaults = getDefaultProps(type);
  return { ...defaults, ...props };
};

/**
 * Get available prop options for a component type
 */
export const getAvailableProps = (type: ComponentType): Record<string, any> => {
  switch (type) {
    case 'button':
      return {
        variant: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
        size: ['default', 'sm', 'lg', 'icon'],
      };

    case 'input':
      return {
        type: ['text', 'email', 'password', 'number', 'tel', 'url'],
      };

    case 'text':
      return {
        tag: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span'],
      };

    case 'container':
      return {
        padding: [0, 8, 16, 24, 32],
        backgroundColor: [
          '#ffffff', '#f3f4f6', '#e5e7eb', '#d1d5db',
          '#fef3c7', '#dbeafe', '#dcfce7', '#fce7f3'
        ],
      };

    case 'multiStepExplain':
      return {
        // MultiStepExplain uses complex configuration handled separately
        steps: 'complex'
      };

    default:
      return {};
  }
};

/**
 * Generate a human-readable label for a component
 */
export const getComponentLabel = (type: ComponentType, props: ComponentProps): string => {
  switch (type) {
    case 'button':
      return `Button (${props.variant || 'default'})`;

    case 'input':
      return `Input (${props.type || 'text'})`;

    case 'text': {
      const text = props.text || 'Sample text';
      const truncated = text.length > 20 ? text.substring(0, 20) + '...' : text;
      return `Text: "${truncated}"`;
    }

    case 'container':
      return 'Container';

    case 'multiStepExplain': {
      const stepCount = props.steps?.length || 0;
      return `Multi-Step Explain (${stepCount} steps)`;
    }

    default:
      return 'Unknown Component';
  }
};

/**
 * Check if a component type supports children
 */
export const supportsChildren = (type: ComponentType): boolean => {
  return type === 'container';
};

/**
 * Get minimum size constraints for a component type
 */
export const getMinimumSize = (type: ComponentType): Size => {
  switch (type) {
    case 'button':
      return { width: 60, height: 32 };

    case 'input':
      return { width: 100, height: 32 };

    case 'text':
      return { width: 50, height: 16 };

    case 'container':
      return { width: 100, height: 100 };

    case 'multiStepExplain':
      return { width: 400, height: 300 };

    default:
      return { width: 50, height: 32 };
  }
};

/**
 * Get maximum size constraints for a component type
 */
export const getMaximumSize = (type: ComponentType): Size => {
  switch (type) {
    case 'button':
      return { width: 400, height: 80 };

    case 'input':
      return { width: 600, height: 60 };

    case 'text':
      return { width: 800, height: 200 };

    case 'container':
      return { width: 1000, height: 800 };

    case 'multiStepExplain':
      return { width: 1200, height: 800 };

    default:
      return { width: 800, height: 600 };
  }
};

/**
 * Grid utility functions for the Component Builder
 * Handles grid snapping, positioning, and layout calculations
 */

import { Position, Size } from '../types';

/**
 * Snap a position to the nearest grid point
 */
export const snapToGrid = (position: Position, gridSize: number): Position => {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
  };
};

/**
 * Snap a size to grid increments
 */
export const snapSizeToGrid = (size: Size, gridSize: number): Size => {
  return {
    width: Math.max(gridSize, Math.round(size.width / gridSize) * gridSize),
    height: Math.max(gridSize, Math.round(size.height / gridSize) * gridSize),
  };
};

/**
 * Calculate the distance between two positions
 */
export const getDistance = (pos1: Position, pos2: Position): number => {
  const dx = pos1.x - pos2.x;
  const dy = pos1.y - pos2.y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Check if a position is within bounds
 */
export const isWithinBounds = (position: Position, size: Size, canvasSize: Size): boolean => {
  return (
    position.x >= 0 &&
    position.y >= 0 &&
    position.x + size.width <= canvasSize.width &&
    position.y + size.height <= canvasSize.height
  );
};

/**
 * Constrain a position to canvas bounds
 */
export const constrainToCanvas = (
  position: Position,
  size: Size,
  canvasSize: Size
): Position => {
  return {
    x: Math.max(0, Math.min(position.x, canvasSize.width - size.width)),
    y: Math.max(0, Math.min(position.y, canvasSize.height - size.height)),
  };
};

/**
 * Generate CSS for grid background
 */
export const getGridBackgroundStyle = (gridSize: number, showGrid: boolean) => {
  if (!showGrid) return {};

  return {
    backgroundImage: `
      linear-gradient(to right, #e5e7eb 1px, transparent 1px),
      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
    `,
    backgroundSize: `${gridSize}px ${gridSize}px`,
  };
};

/**
 * Calculate smart spacing suggestions between components
 */
export const getSpacingSuggestions = (
  targetPosition: Position,
  targetSize: Size,
  existingComponents: Array<{ position: Position; size: Size; id: string }>,
  gridSize: number,
  excludeId?: string
): Position[] => {
  const suggestions: Position[] = [];
  const snapDistance = gridSize * 2; // Suggest snapping within 2 grid units

  existingComponents.forEach((component) => {
    if (component.id === excludeId) return;

    const { position: compPos, size: compSize } = component;

    // Horizontal alignment suggestions
    // Align left edges
    if (Math.abs(targetPosition.x - compPos.x) <= snapDistance) {
      suggestions.push({ x: compPos.x, y: targetPosition.y });
    }

    // Align right edges
    const compRight = compPos.x + compSize.width;
    const targetRight = targetPosition.x + targetSize.width;
    if (Math.abs(targetRight - compRight) <= snapDistance) {
      suggestions.push({ x: compRight - targetSize.width, y: targetPosition.y });
    }

    // Align centers horizontally
    const compCenterX = compPos.x + compSize.width / 2;
    const targetCenterX = targetPosition.x + targetSize.width / 2;
    if (Math.abs(targetCenterX - compCenterX) <= snapDistance) {
      suggestions.push({ x: compCenterX - targetSize.width / 2, y: targetPosition.y });
    }

    // Vertical alignment suggestions
    // Align top edges
    if (Math.abs(targetPosition.y - compPos.y) <= snapDistance) {
      suggestions.push({ x: targetPosition.x, y: compPos.y });
    }

    // Align bottom edges
    const compBottom = compPos.y + compSize.height;
    const targetBottom = targetPosition.y + targetSize.height;
    if (Math.abs(targetBottom - compBottom) <= snapDistance) {
      suggestions.push({ x: targetPosition.x, y: compBottom - targetSize.height });
    }

    // Align centers vertically
    const compCenterY = compPos.y + compSize.height / 2;
    const targetCenterY = targetPosition.y + targetSize.height / 2;
    if (Math.abs(targetCenterY - compCenterY) <= snapDistance) {
      suggestions.push({ x: targetPosition.x, y: compCenterY - targetSize.height / 2 });
    }

    // Spacing suggestions (place next to components with standard spacing)
    const standardSpacing = gridSize * 2;

    // Place to the right
    suggestions.push({
      x: compPos.x + compSize.width + standardSpacing,
      y: targetPosition.y,
    });

    // Place to the left
    suggestions.push({
      x: compPos.x - targetSize.width - standardSpacing,
      y: targetPosition.y,
    });

    // Place below
    suggestions.push({
      x: targetPosition.x,
      y: compPos.y + compSize.height + standardSpacing,
    });

    // Place above
    suggestions.push({
      x: targetPosition.x,
      y: compPos.y - targetSize.height - standardSpacing,
    });
  });

  // Remove duplicates and invalid positions
  const uniqueSuggestions = suggestions.filter((suggestion, index, array) => {
    // Remove duplicates
    const isDuplicate = array.findIndex(
      (s) => Math.abs(s.x - suggestion.x) < 1 && Math.abs(s.y - suggestion.y) < 1
    ) !== index;

    if (isDuplicate) return false;

    // Remove positions that would place component outside canvas
    return suggestion.x >= 0 && suggestion.y >= 0;
  });

  return uniqueSuggestions;
};

/**
 * Find the closest grid point to a position
 */
export const getClosestGridPoint = (position: Position, gridSize: number): Position => {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
  };
};

/**
 * Calculate grid lines for rendering
 */
export const getGridLines = (canvasSize: Size, gridSize: number) => {
  const verticalLines: number[] = [];
  const horizontalLines: number[] = [];

  // Generate vertical lines
  for (let x = 0; x <= canvasSize.width; x += gridSize) {
    verticalLines.push(x);
  }

  // Generate horizontal lines
  for (let y = 0; y <= canvasSize.height; y += gridSize) {
    horizontalLines.push(y);
  }

  return { verticalLines, horizontalLines };
};

/**
 * Icon mapping utility for MultiStepExplain component
 * Maps icon names to actual lucide-react components
 */

import React from 'react';
import {
  Brain,
  BookOpen,
  Zap,
  MessageSquare,
  TrendingUp,
  Calculator,
  MapPin,
  Lightbulb,
  Search,
  Scale,
  DollarSign,
  ShoppingCart,
  AlertTriangle,
  Settings,
  Users,
  FileText,
  Home,
  Star,
  Heart,
  Target,
  Rocket,
  Globe,
  Lock,
  Key,
  Mail,
  Phone,
  Calendar,
  Clock,
  Camera,
  Image,
  Video,
  Music,
  Download,
  Upload,
  Share,
  Edit,
  Trash,
  Plus,
  Minus,
  Check,
  X,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Info,
  HelpCircle,
  AlertCircle,
  CheckCircle,
  XCircle,
  Award,
  Flag
} from 'lucide-react';

export type IconName =
  | 'Brain'
  | 'BookOpen'
  | 'Zap'
  | 'MessageSquare'
  | 'TrendingUp'
  | 'Calculator'
  | 'MapPin'
  | 'Lightbulb'
  | 'Search'
  | 'Scale'
  | 'DollarSign'
  | 'ShoppingCart'
  | 'AlertTriangle'
  | 'Settings'
  | 'Users'
  | 'FileText'
  | 'Home'
  | 'Star'
  | 'Heart'
  | 'Target'
  | 'Rocket'
  | 'Globe'
  | 'Lock'
  | 'Key'
  | 'Mail'
  | 'Phone'
  | 'Calendar'
  | 'Clock'
  | 'Camera'
  | 'Image'
  | 'Video'
  | 'Music'
  | 'Download'
  | 'Upload'
  | 'Share'
  | 'Edit'
  | 'Trash'
  | 'Plus'
  | 'Minus'
  | 'Check'
  | 'X'
  | 'ArrowRight'
  | 'ArrowLeft'
  | 'ArrowUp'
  | 'ArrowDown'
  | 'Info'
  | 'HelpCircle'
  | 'AlertCircle'
  | 'CheckCircle'
  | 'XCircle'
  | 'Award'
  | 'Flag';

export const ICON_MAP: Record<IconName, React.ComponentType<{ className?: string }>> = {
  Brain,
  BookOpen,
  Zap,
  MessageSquare,
  TrendingUp,
  Calculator,
  MapPin,
  Lightbulb,
  Search,
  Scale,
  DollarSign,
  ShoppingCart,
  AlertTriangle,
  Settings,
  Users,
  FileText,
  Home,
  Star,
  Heart,
  Target,
  Rocket,
  Globe,
  Lock,
  Key,
  Mail,
  Phone,
  Calendar,
  Clock,
  Camera,
  Image,
  Video,
  Music,
  Download,
  Upload,
  Share,
  Edit,
  Trash,
  Plus,
  Minus,
  Check,
  X,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Info,
  HelpCircle,
  AlertCircle,
  CheckCircle,
  XCircle,
  Award,
  Flag,
};

export const ICON_OPTIONS: { value: IconName; label: string }[] = [
  { value: 'Brain', label: '🧠 Brain' },
  { value: 'BookOpen', label: '📖 Book Open' },
  { value: 'Zap', label: '⚡ Zap' },
  { value: 'MessageSquare', label: '💬 Message' },
  { value: 'TrendingUp', label: '📈 Trending Up' },
  { value: 'Calculator', label: '🧮 Calculator' },
  { value: 'MapPin', label: '📍 Map Pin' },
  { value: 'Lightbulb', label: '💡 Lightbulb' },
  { value: 'Search', label: '🔍 Search' },
  { value: 'Scale', label: '⚖️ Scale' },
  { value: 'DollarSign', label: '💲 Dollar' },
  { value: 'ShoppingCart', label: '🛒 Shopping Cart' },
  { value: 'AlertTriangle', label: '⚠️ Alert' },
  { value: 'Settings', label: '⚙️ Settings' },
  { value: 'Users', label: '👥 Users' },
  { value: 'FileText', label: '📄 File' },
  { value: 'Home', label: '🏠 Home' },
  { value: 'Star', label: '⭐ Star' },
  { value: 'Heart', label: '❤️ Heart' },
  { value: 'Target', label: '🎯 Target' },
  { value: 'Rocket', label: '🚀 Rocket' },
  { value: 'Globe', label: '🌍 Globe' },
  { value: 'Lock', label: '🔒 Lock' },
  { value: 'Key', label: '🔑 Key' },
  { value: 'Mail', label: '📧 Mail' },
  { value: 'Phone', label: '📞 Phone' },
  { value: 'Calendar', label: '📅 Calendar' },
  { value: 'Clock', label: '🕐 Clock' },
  { value: 'Camera', label: '📷 Camera' },
  { value: 'Image', label: '🖼️ Image' },
  { value: 'Video', label: '🎥 Video' },
  { value: 'Music', label: '🎵 Music' },
  { value: 'Download', label: '⬇️ Download' },
  { value: 'Upload', label: '⬆️ Upload' },
  { value: 'Share', label: '📤 Share' },
  { value: 'Edit', label: '✏️ Edit' },
  { value: 'Trash', label: '🗑️ Trash' },
  { value: 'Plus', label: '➕ Plus' },
  { value: 'Minus', label: '➖ Minus' },
  { value: 'Check', label: '✅ Check' },
  { value: 'X', label: '❌ X' },
  { value: 'ArrowRight', label: '➡️ Arrow Right' },
  { value: 'ArrowLeft', label: '⬅️ Arrow Left' },
  { value: 'ArrowUp', label: '⬆️ Arrow Up' },
  { value: 'ArrowDown', label: '⬇️ Arrow Down' },
  { value: 'Info', label: 'ℹ️ Info' },
  { value: 'HelpCircle', label: '❓ Help' },
  { value: 'AlertCircle', label: '⚠️ Alert Circle' },
  { value: 'CheckCircle', label: '✅ Check Circle' },
  { value: 'XCircle', label: '❌ X Circle' },
  { value: 'Award', label: '🏆 Award' },
  { value: 'Flag', label: '🚩 Flag' },
];

/**
 * Get icon component by name
 */
export const getIconByName = (iconName: IconName): React.ComponentType<{ className?: string }> | null => {
  return ICON_MAP[iconName] || null;
};

/**
 * Get icon element by name (for direct rendering)
 */
export const getIconElement = (iconName: IconName, className = "w-8 h-8"): React.ReactElement | null => {
  const IconComponent = ICON_MAP[iconName];
  return IconComponent ? React.createElement(IconComponent, { className }) : null;
};

/**
 * Get default icon for content type
 */
export const getDefaultIconForContentType = (contentType: string): IconName => {
  const typeIconMap: Record<string, IconName> = {
    paragraph: 'FileText',
    infoBox: 'Info',
    bulletList: 'ArrowRight',
    numberedList: 'ArrowRight',
    grid: 'Target',
    comparison: 'Scale',
    table: 'Calculator',
    scatterPlot: 'TrendingUp',
    keyValueGrid: 'Key',
  };

  return typeIconMap[contentType] || 'Brain';
};

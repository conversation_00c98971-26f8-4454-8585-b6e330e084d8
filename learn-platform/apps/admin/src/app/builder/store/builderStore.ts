/**
 * Zustand store for Component Builder state management
 * Handles all builder state including components, canvas settings, and persistence
 */

import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { BuilderState, ComponentInstance, ComponentType, Position, Size, ConditionalRule } from '../types';
import { DEFAULT_COMPONENT_SIZES, DEFAULT_COMPONENT_PROPS } from '../utils/componentFactory';

const STORAGE_KEY = 'component-builder-canvas';
const DEFAULT_GRID_SIZE = 16;
const DEFAULT_CANVAS_SIZE = { width: 1200, height: 800 };

/**
 * Utility function to snap position to grid
 */
const snapToGrid = (position: Position, gridSize: number, shouldSnap: boolean): Position => {
  if (!shouldSnap) return position;

  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
  };
};

/**
 * Load canvas state from localStorage
 */
const loadCanvasFromStorage = () => {
  if (typeof window === 'undefined') return null;

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error('Failed to load canvas from storage:', error);
    return null;
  }
};

/**
 * Save canvas state to localStorage
 */
const saveCanvasToStorage = (state: Partial<BuilderState>) => {
  if (typeof window === 'undefined') return;

  try {
    const canvasData = {
      components: state.components,
      canvasSize: state.canvasSize,
      gridSize: state.gridSize,
      snapToGrid: state.snapToGrid,
      showGrid: state.showGrid,
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(canvasData));
  } catch (error) {
    console.error('Failed to save canvas to storage:', error);
  }
};

/**
 * Create the builder store
 */
export const useBuilderStore = create<BuilderState>((set, get) => {
  // Load initial state from localStorage or use defaults
  const savedState = loadCanvasFromStorage();

  const initialState = {
    components: savedState?.components || [],
    selectedComponentId: null,
    canvasSize: savedState?.canvasSize || DEFAULT_CANVAS_SIZE,
    gridSize: savedState?.gridSize || DEFAULT_GRID_SIZE,
    snapToGrid: savedState?.snapToGrid ?? true,
    showGrid: savedState?.showGrid ?? true,
  };

  return {
    ...initialState,

    // Component management actions
    addComponent: (type: ComponentType, position: Position) => {
      const state = get();
      const snappedPosition = snapToGrid(position, state.gridSize, state.snapToGrid);

      // Use defaults from componentFactory
      const defaultSizes = DEFAULT_COMPONENT_SIZES;
      const defaultProps = DEFAULT_COMPONENT_PROPS;

      const newComponent: ComponentInstance = {
        id: uuidv4(),
        type,
        position: snappedPosition,
        size: defaultSizes[type],
        props: defaultProps[type],
        zIndex: state.components.length,
      };

      set((state) => ({
        components: [...state.components, newComponent],
        selectedComponentId: newComponent.id,
      }));
    },

    removeComponent: (id: string) => {
      set((state) => ({
        components: state.components.filter(c => c.id !== id),
        selectedComponentId: state.selectedComponentId === id ? null : state.selectedComponentId,
      }));
    },

    updateComponent: (id: string, updates: Partial<ComponentInstance>) => {
      set((state) => ({
        components: state.components.map(c =>
          c.id === id ? { ...c, ...updates } : c
        ),
      }));
    },

    selectComponent: (id: string | null) => {
      set({ selectedComponentId: id });
    },

    moveComponent: (id: string, position: Position) => {
      const state = get();
      const snappedPosition = snapToGrid(position, state.gridSize, state.snapToGrid);

      set((state) => ({
        components: state.components.map(c =>
          c.id === id ? { ...c, position: snappedPosition } : c
        ),
      }));
    },

    resizeComponent: (id: string, size: Size) => {
      set((state) => ({
        components: state.components.map(c =>
          c.id === id ? { ...c, size } : c
        ),
      }));
    },

    // Canvas settings actions
    setGridSize: (size: number) => {
      set({ gridSize: size });
    },

    toggleSnapToGrid: () => {
      set((state) => ({ snapToGrid: !state.snapToGrid }));
    },

    toggleShowGrid: () => {
      set((state) => ({ showGrid: !state.showGrid }));
    },

    setCanvasSize: (size: Size) => {
      set({ canvasSize: size });
    },

    // Conditional rules actions
    addConditionalRule: (componentId: string, rule: ConditionalRule) => {
      set((state) => ({
        components: state.components.map(c =>
          c.id === componentId
            ? { ...c, conditionalRules: [...(c.conditionalRules || []), rule] }
            : c
        ),
      }));
    },

    removeConditionalRule: (componentId: string, ruleId: string) => {
      set((state) => ({
        components: state.components.map(c =>
          c.id === componentId
            ? { ...c, conditionalRules: c.conditionalRules?.filter(r => r.id !== ruleId) }
            : c
        ),
      }));
    },

    updateConditionalRule: (componentId: string, ruleId: string, updates: Partial<ConditionalRule>) => {
      set((state) => ({
        components: state.components.map(c =>
          c.id === componentId
            ? {
                ...c,
                conditionalRules: c.conditionalRules?.map(r =>
                  r.id === ruleId ? { ...r, ...updates } : r
                ),
              }
            : c
        ),
      }));
    },

    // Persistence actions
    saveCanvas: () => {
      const state = get();
      saveCanvasToStorage(state);
    },

    loadCanvas: () => {
      const savedState = loadCanvasFromStorage();
      if (savedState) {
        set({
          components: savedState.components || [],
          canvasSize: savedState.canvasSize || DEFAULT_CANVAS_SIZE,
          gridSize: savedState.gridSize || DEFAULT_GRID_SIZE,
          snapToGrid: savedState.snapToGrid ?? true,
          showGrid: savedState.showGrid ?? true,
          selectedComponentId: null,
        });
      }
    },

    clearCanvas: () => {
      set({
        components: [],
        selectedComponentId: null,
        canvasSize: DEFAULT_CANVAS_SIZE,
        gridSize: DEFAULT_GRID_SIZE,
        snapToGrid: true,
        showGrid: true,
      });
      saveCanvasToStorage({
        components: [],
        canvasSize: DEFAULT_CANVAS_SIZE,
        gridSize: DEFAULT_GRID_SIZE,
        snapToGrid: true,
        showGrid: true,
      });
    },

    // Utility actions
    getComponent: (id: string) => {
      return get().components.find(c => c.id === id);
    },

    getSelectedComponent: () => {
      const state = get();
      return state.selectedComponentId
        ? state.components.find(c => c.id === state.selectedComponentId)
        : undefined;
    },
  };
});

// Auto-save functionality
if (typeof window !== 'undefined') {
  useBuilderStore.subscribe((state) => {
    saveCanvasToStorage(state);
  });
}

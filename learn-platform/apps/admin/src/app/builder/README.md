# Component Builder Interface

A visual, low-code component builder for creating UI interfaces with drag-and-drop functionality.

## 🎯 Features

### Core Functionality
- **Drag-and-Drop Canvas**: Visual interface for placing and arranging components
- **Grid-Based Layout**: 16px grid system with snap-to-grid alignment
- **Component Palette**: Pre-built UI elements (Button, Input, Text, Container)
- **Properties Panel**: Real-time editing of component properties
- **Conditional Rendering**: Show/hide components based on simple conditions
- **Persistence**: Auto-save to localStorage with manual save/load/export

### Component Types
1. **Button**: Configurable variant, size, and text
2. **Input**: Different input types with placeholder text
3. **Text**: Headings (h1-h6), paragraphs, and spans with custom content
4. **Container**: Layout containers with padding and background colors

### Advanced Features
- **Smart Spacing**: Automatic alignment suggestions between components
- **Visual Feedback**: Selection indicators and drag overlays
- **Grid Controls**: Toggle grid visibility and snap-to-grid behavior
- **Export Functionality**: JSON export of canvas state

## 🏗️ Architecture

### State Management
- **Zustand Store**: Lightweight state management for canvas state
- **Immutable Updates**: Clean state updates with automatic persistence
- **Component Selection**: Single-selection model with property editing

### Drag & Drop
- **@dnd-kit**: Modern drag-and-drop library with excellent TypeScript support
- **Collision Detection**: Smart drop zone detection
- **Visual Feedback**: Drag overlays and drop indicators

### Component System
- **Factory Pattern**: Centralized component creation and configuration
- **Wrapper Components**: Builder-aware components with selection and editing
- **Type Safety**: Full TypeScript coverage for all component types

## 📁 File Structure

```
apps/admin/src/app/builder/
├── page.tsx                    # Main builder page
├── types.ts                    # TypeScript definitions
├── store/
│   └── builderStore.ts         # Zustand state management
├── utils/
│   ├── gridUtils.ts            # Grid snapping and positioning
│   └── componentFactory.ts     # Component creation utilities
└── components/
    ├── Canvas/                 # Canvas and drop zone components
    │   ├── Canvas.tsx
    │   ├── CanvasGrid.tsx
    │   └── DropZone.tsx
    ├── ComponentPalette/       # Draggable component library
    │   ├── ComponentPalette.tsx
    │   └── PaletteItem.tsx
    ├── RenderedComponents/     # Builder-aware component wrappers
    │   ├── RenderableButton.tsx
    │   ├── RenderableInput.tsx
    │   ├── RenderableText.tsx
    │   └── RenderableContainer.tsx
    ├── PropertiesPanel/        # Component property editor
    │   ├── PropertiesPanel.tsx
    │   └── ConditionalRules.tsx
    └── Toolbar/               # Save/load/settings toolbar
        └── Toolbar.tsx
```

## 🚀 Usage

### Basic Workflow
1. **Navigate** to `/builder` in the admin panel
2. **Drag** components from the palette to the canvas
3. **Click** components to select and edit properties
4. **Position** components using drag or manual coordinates
5. **Save** your work using the toolbar

### Grid System
- **16px Grid**: Default grid size for consistent spacing
- **Snap to Grid**: Toggle automatic snapping behavior
- **Visual Grid**: Show/hide grid overlay
- **Custom Grid Sizes**: 8px, 16px, 24px, or 32px options

### Component Properties
- **Position**: X/Y coordinates with manual input
- **Size**: Width/height with minimum constraints
- **Type-Specific Props**: Variant, size, text content, etc.
- **Conditional Rules**: Simple show/hide logic

### Conditional Rendering
Create simple conditions like:
- `state.isVisible === true`
- `state.userRole === 'admin'`
- `state.count > 5`

Actions available:
- **Show**: Display the component
- **Hide**: Hide the component
- **Disable**: Disable component interaction

## 🔧 Technical Details

### Dependencies
- **zustand**: State management
- **@dnd-kit/core**: Drag and drop functionality
- **@dnd-kit/sortable**: Sortable interactions
- **@dnd-kit/utilities**: Utility functions
- **uuid**: Unique ID generation

### Integration
- **Admin Layout**: Integrated with existing admin panel
- **Protected Routes**: Requires authentication
- **Shared UI**: Uses existing shadcn/ui components
- **Responsive Design**: Works on desktop and tablet

### Performance
- **React.memo**: Optimized component re-rendering
- **Efficient Updates**: Minimal state changes
- **Auto-save**: Debounced localStorage persistence

## 🎨 Styling

### Design System
- **Tailwind CSS**: Utility-first styling
- **Consistent Colors**: Blue accent colors for selection
- **Grid Overlay**: Subtle gray grid lines
- **Hover States**: Interactive feedback

### Responsive Behavior
- **Desktop First**: Optimized for desktop usage
- **Tablet Support**: Functional on larger tablets
- **Mobile**: Limited support (admin tool focus)

## 🔮 Future Enhancements

### Planned Features
- **Undo/Redo**: Action history management
- **Component Grouping**: Multi-select and grouping
- **Template System**: Pre-built component templates
- **Code Generation**: Export to React component code
- **Advanced Conditions**: Expression builder interface
- **Collaborative Editing**: Real-time collaboration
- **Component Library**: Custom component definitions

### Possible Integrations
- **Database Persistence**: Save projects to database
- **Version Control**: Project versioning and history
- **Preview Mode**: Live preview of generated components
- **Theme Support**: Multiple design themes
- **Asset Management**: Image and icon uploads

## 📝 Development Notes

### Adding New Component Types
1. Update `ComponentType` in `types.ts`
2. Add default props in `componentFactory.ts`
3. Create renderable component in `RenderedComponents/`
4. Update canvas rendering logic
5. Add property editors in `PropertiesPanel`

### Extending Properties
1. Update `ComponentProps` interface
2. Add to `getAvailableProps()` function
3. Update property panel rendering
4. Test with existing components

### State Management Patterns
- Use Zustand actions for all state changes
- Maintain immutability with spread operators
- Auto-save on every state change
- Provide manual save/load for user control

This Component Builder provides a solid foundation for visual component creation while maintaining clean, extensible code architecture.

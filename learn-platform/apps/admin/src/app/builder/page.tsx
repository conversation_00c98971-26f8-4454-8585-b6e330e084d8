/**
 * Component Builder Page - Main interface for the low-code component builder
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@learn-platform/shared-ui';
import { Sparkles, ArrowRight } from 'lucide-react';
import { ProtectedRoute } from '../../components/auth/protected-route';
import { AdminLayout } from '../../lib/components/layout';
import { ComponentPalette } from './components/ComponentPalette';
import { Canvas } from './components/Canvas';
import { PropertiesPanel } from './components/PropertiesPanel/PropertiesPanel';
import { Toolbar } from './components/Toolbar';
import { DragDropProvider } from './components/DragDropProvider';

export default function ComponentBuilderPage() {
  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <DragDropProvider>
          <div className="h-full flex flex-col">
            {/* Page Header */}
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    🎨 Component Builder
                  </h1>
                  <p className="text-gray-600">
                    Create and design UI components with our visual drag-and-drop interface
                  </p>
                </div>

                {/* Link to Explainer Builder */}
                <Link href="/builder/explainer">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    Try Explainer Builder
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </div>

            {/* Quick Access Cards */}
            <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Component Builder Card */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    🎨
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Component Builder</h3>
                    <p className="text-sm text-gray-600">Drag-and-drop interface</p>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Create UI components with visual positioning and property editing.
                </p>
                <div className="text-xs text-gray-500">
                  Current page - scroll down to start building
                </div>
              </div>

              {/* Explainer Builder Card */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <Sparkles className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Explainer Builder</h3>
                    <p className="text-sm text-blue-700">Block-based editor</p>
                  </div>
                </div>
                <p className="text-sm text-gray-700 mb-4">
                  Create step-by-step explanations with inline editing and rich content types.
                </p>
                <Link href="/builder/explainer">
                  <Button size="sm" className="w-full">
                    Try Explainer Builder
                  </Button>
                </Link>
              </div>
            </div>

            {/* Toolbar */}
            <div className="mb-4">
              <Toolbar />
            </div>

            {/* Main Builder Interface */}
            <div className="flex-1 flex gap-4 min-h-0">
              {/* Left Panel - Component Palette */}
              <div className="flex-shrink-0">
                <ComponentPalette />
              </div>

              {/* Center - Canvas */}
              <div className="flex-1 min-w-0">
                <Canvas />
              </div>

              {/* Right Panel - Properties */}
              <div className="flex-shrink-0">
                <PropertiesPanel />
              </div>
            </div>
          </div>
        </DragDropProvider>
      </AdminLayout>
    </ProtectedRoute>
  );
}

/**
 * MultiStepExplain Editor for the Properties Panel
 * Provides a sophisticated interface for configuring steps
 */

'use client';

import React, { useState } from 'react';
import { Button, Input } from '@learn-platform/shared-ui';
import { Plus, Minus, Eye, ChevronDown, ChevronUp } from 'lucide-react';
import { StepConfig } from '../../../../lib/components/templates/types';
import { IconName, ICON_OPTIONS } from '../../utils/iconMapping';
import { MultiStepPreviewModal } from './MultiStepPreviewModal';

interface MultiStepExplainEditorProps {
  steps: StepConfig[];
  onChange: (steps: StepConfig[]) => void;
}

const CONTENT_TYPE_OPTIONS = [
  { value: 'paragraph', label: 'Paragraph' },
  { value: 'infoBox', label: 'Info Box' },
  { value: 'bulletList', label: 'Bullet List' },
  { value: 'numberedList', label: 'Numbered List' },
  { value: 'grid', label: 'Grid Layout' },
  { value: 'comparison', label: 'Comparison' },
  { value: 'table', label: 'Table' },
  { value: 'scatterPlot', label: 'Scatter Plot' },
  { value: 'keyValueGrid', label: 'Key-Value Grid' },
];

export const MultiStepExplainEditor: React.FC<MultiStepExplainEditorProps> = ({
  steps,
  onChange,
}) => {
  const [expandedStep, setExpandedStep] = useState<number | null>(0);
  const [showPreview, setShowPreview] = useState(false);

  const addStep = () => {
    const newStep: StepConfig = {
      title: 'New Step',
      icon: 'Brain', // Store as string
      type: 'paragraph',
      data: 'Enter your content here...'
    };
    onChange([...steps, newStep]);
    setExpandedStep(steps.length);
  };

  const removeStep = (index: number) => {
    const newSteps = steps.filter((_, i) => i !== index);
    onChange(newSteps);
    if (expandedStep === index) {
      setExpandedStep(null);
    } else if (expandedStep !== null && expandedStep > index) {
      setExpandedStep(expandedStep - 1);
    }
  };

  const updateStep = (index: number, updates: Partial<StepConfig>) => {
    const newSteps = steps.map((step, i) =>
      i === index ? { ...step, ...updates } : step
    );
    onChange(newSteps);
  };

  const updateStepIcon = (index: number, iconName: IconName) => {
    updateStep(index, { icon: iconName }); // Store as string
  };

  const updateStepData = (index: number, data: any) => {
    updateStep(index, { data });
  };

  const renderContentEditor = (step: StepConfig, index: number) => {
    switch (step.type) {
      case 'paragraph': {
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content
            </label>
            <textarea
              value={Array.isArray(step.data) ? step.data.join('\n\n') : step.data}
              onChange={(e) => {
                const value = e.target.value;
                const paragraphs = value.split('\n\n').filter(p => p.trim());
                updateStepData(index, paragraphs.length > 1 ? paragraphs : value);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={4}
              placeholder="Enter paragraph content..."
            />
          </div>
        );
      }

      case 'infoBox': {
        const infoData = step.data || { heading: '', lines: [''] };
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Heading (optional)
              </label>
              <Input
                value={infoData.heading || ''}
                onChange={(e) => updateStepData(index, { ...infoData, heading: e.target.value })}
                placeholder="Info box heading..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lines
              </label>
              {infoData.lines.map((line: string, lineIndex: number) => (
                <div key={lineIndex} className="flex gap-2 mb-2">
                  <Input
                    value={line}
                    onChange={(e) => {
                      const newLines = [...infoData.lines];
                      newLines[lineIndex] = e.target.value;
                      updateStepData(index, { ...infoData, lines: newLines });
                    }}
                    placeholder={`Line ${lineIndex + 1}...`}
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newLines = infoData.lines.filter((_: any, i: number) => i !== lineIndex);
                      updateStepData(index, { ...infoData, lines: newLines });
                    }}
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  updateStepData(index, { ...infoData, lines: [...infoData.lines, ''] });
                }}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Line
              </Button>
            </div>
          </div>
        );
      }

      case 'bulletList':
      case 'numberedList': {
        const listData = Array.isArray(step.data) ? step.data : [''];
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              List Items
            </label>
            {listData.map((item: string, itemIndex: number) => (
              <div key={itemIndex} className="flex gap-2 mb-2">
                <Input
                  value={item}
                  onChange={(e) => {
                    const newItems = [...listData];
                    newItems[itemIndex] = e.target.value;
                    updateStepData(index, newItems);
                  }}
                  placeholder={`Item ${itemIndex + 1}...`}
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newItems = listData.filter((_: any, i: number) => i !== itemIndex);
                    updateStepData(index, newItems);
                  }}
                >
                  <Minus className="w-4 h-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateStepData(index, [...listData, ''])}
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Item
            </Button>
          </div>
        );
      }

      case 'grid': {
        const gridData = Array.isArray(step.data) ? step.data : [{ title: '', content: '' }];
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Grid Items
            </label>
            {gridData.map((item: any, itemIndex: number) => (
              <div key={itemIndex} className="border border-gray-200 rounded p-3 mb-3">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Item {itemIndex + 1}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newItems = gridData.filter((_: any, i: number) => i !== itemIndex);
                      updateStepData(index, newItems);
                    }}
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                </div>
                <div className="space-y-2">
                  <Input
                    value={item.title || ''}
                    onChange={(e) => {
                      const newItems = [...gridData];
                      newItems[itemIndex] = { ...item, title: e.target.value };
                      updateStepData(index, newItems);
                    }}
                    placeholder="Title..."
                  />
                  <textarea
                    value={item.content || ''}
                    onChange={(e) => {
                      const newItems = [...gridData];
                      newItems[itemIndex] = { ...item, content: e.target.value };
                      updateStepData(index, newItems);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={2}
                    placeholder="Content..."
                  />
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateStepData(index, [...gridData, { title: '', content: '' }])}
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Grid Item
            </Button>
          </div>
        );
      }

      case 'keyValueGrid': {
        const kvData = Array.isArray(step.data) ? step.data : [{ key: '', value: '' }];
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Key-Value Pairs
            </label>
            {kvData.map((item: any, itemIndex: number) => (
              <div key={itemIndex} className="flex gap-2 mb-2">
                <Input
                  value={item.key || ''}
                  onChange={(e) => {
                    const newItems = [...kvData];
                    newItems[itemIndex] = { ...item, key: e.target.value };
                    updateStepData(index, newItems);
                  }}
                  placeholder="Key..."
                  className="flex-1"
                />
                <Input
                  value={item.value || ''}
                  onChange={(e) => {
                    const newItems = [...kvData];
                    newItems[itemIndex] = { ...item, value: e.target.value };
                    updateStepData(index, newItems);
                  }}
                  placeholder="Value..."
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newItems = kvData.filter((_: any, i: number) => i !== itemIndex);
                    updateStepData(index, newItems);
                  }}
                >
                  <Minus className="w-4 h-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateStepData(index, [...kvData, { key: '', value: '' }])}
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Pair
            </Button>
          </div>
        );
      }

      case 'comparison': {
        const compData = Array.isArray(step.data) ? step.data : [{ label: '', before: '', after: '' }];
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comparisons
            </label>
            {compData.map((item: any, itemIndex: number) => (
              <div key={itemIndex} className="border border-gray-200 rounded p-3 mb-3">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Comparison {itemIndex + 1}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newItems = compData.filter((_: any, i: number) => i !== itemIndex);
                      updateStepData(index, newItems);
                    }}
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                </div>
                <div className="space-y-2">
                  <Input
                    value={item.label || ''}
                    onChange={(e) => {
                      const newItems = [...compData];
                      newItems[itemIndex] = { ...item, label: e.target.value };
                      updateStepData(index, newItems);
                    }}
                    placeholder="Comparison label..."
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <textarea
                      value={item.before || ''}
                      onChange={(e) => {
                        const newItems = [...compData];
                        newItems[itemIndex] = { ...item, before: e.target.value };
                        updateStepData(index, newItems);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={2}
                      placeholder="Before..."
                    />
                    <textarea
                      value={item.after || ''}
                      onChange={(e) => {
                        const newItems = [...compData];
                        newItems[itemIndex] = { ...item, after: e.target.value };
                        updateStepData(index, newItems);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={2}
                      placeholder="After..."
                    />
                  </div>
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateStepData(index, [...compData, { label: '', before: '', after: '' }])}
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Comparison
            </Button>
          </div>
        );
      }

      case 'table': {
        const tableData = step.data || { headers: [''], rows: [['']] };
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Headers
              </label>
              <div className="flex gap-2 mb-2">
                {tableData.headers.map((header: string, headerIndex: number) => (
                  <div key={headerIndex} className="flex gap-1">
                    <Input
                      value={header}
                      onChange={(e) => {
                        const newHeaders = [...tableData.headers];
                        newHeaders[headerIndex] = e.target.value;
                        updateStepData(index, { ...tableData, headers: newHeaders });
                      }}
                      placeholder={`Header ${headerIndex + 1}`}
                      className="w-24"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newHeaders = tableData.headers.filter((_: any, i: number) => i !== headerIndex);
                        const newRows = tableData.rows.map((row: any[]) =>
                          row.filter((_: any, i: number) => i !== headerIndex)
                        );
                        updateStepData(index, { headers: newHeaders, rows: newRows });
                      }}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newHeaders = [...tableData.headers, ''];
                    const newRows = tableData.rows.map((row: any[]) => [...row, '']);
                    updateStepData(index, { headers: newHeaders, rows: newRows });
                  }}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rows
              </label>
              {tableData.rows.map((row: any[], rowIndex: number) => (
                <div key={rowIndex} className="flex gap-2 mb-2">
                  {row.map((cell: any, cellIndex: number) => (
                    <Input
                      key={cellIndex}
                      value={cell}
                      onChange={(e) => {
                        const newRows = [...tableData.rows];
                        newRows[rowIndex][cellIndex] = e.target.value;
                        updateStepData(index, { ...tableData, rows: newRows });
                      }}
                      placeholder={`Cell ${rowIndex + 1},${cellIndex + 1}`}
                      className="w-24"
                    />
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newRows = tableData.rows.filter((_: any, i: number) => i !== rowIndex);
                      updateStepData(index, { ...tableData, rows: newRows });
                    }}
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newRow = new Array(tableData.headers.length).fill('');
                  updateStepData(index, { ...tableData, rows: [...tableData.rows, newRow] });
                }}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Row
              </Button>
            </div>
          </div>
        );
      }

      case 'scatterPlot': {
        const plotData = step.data || { data: [{ x: 0, y: 0, label: '' }], width: 400, height: 300 };
        return (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Width</label>
                <Input
                  type="number"
                  value={plotData.width || 400}
                  onChange={(e) => {
                    updateStepData(index, { ...plotData, width: parseInt(e.target.value) || 400 });
                  }}
                  placeholder="400"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Height</label>
                <Input
                  type="number"
                  value={plotData.height || 300}
                  onChange={(e) => {
                    updateStepData(index, { ...plotData, height: parseInt(e.target.value) || 300 });
                  }}
                  placeholder="300"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data Points
              </label>
              {plotData.data.map((point: any, pointIndex: number) => (
                <div key={pointIndex} className="border border-gray-200 rounded p-2 mb-2">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Point {pointIndex + 1}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newData = plotData.data.filter((_: any, i: number) => i !== pointIndex);
                        updateStepData(index, { ...plotData, data: newData });
                      }}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <Input
                      type="number"
                      value={point.x || 0}
                      onChange={(e) => {
                        const newData = [...plotData.data];
                        newData[pointIndex] = { ...point, x: parseFloat(e.target.value) || 0 };
                        updateStepData(index, { ...plotData, data: newData });
                      }}
                      placeholder="X"
                    />
                    <Input
                      type="number"
                      value={point.y || 0}
                      onChange={(e) => {
                        const newData = [...plotData.data];
                        newData[pointIndex] = { ...point, y: parseFloat(e.target.value) || 0 };
                        updateStepData(index, { ...plotData, data: newData });
                      }}
                      placeholder="Y"
                    />
                    <Input
                      value={point.label || ''}
                      onChange={(e) => {
                        const newData = [...plotData.data];
                        newData[pointIndex] = { ...point, label: e.target.value };
                        updateStepData(index, { ...plotData, data: newData });
                      }}
                      placeholder="Label"
                    />
                  </div>
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newData = [...plotData.data, { x: 0, y: 0, label: '' }];
                  updateStepData(index, { ...plotData, data: newData });
                }}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Point
              </Button>
            </div>
          </div>
        );
      }

      default:
        return (
          <div className="text-gray-500 text-sm">
            Content editor for &quot;{step.type}&quot; is not yet implemented.
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Steps Configuration</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowPreview(true)}
          >
            <Eye className="w-4 h-4 mr-1" />
            Preview
          </Button>
          <Button size="sm" onClick={addStep}>
            <Plus className="w-4 h-4 mr-1" />
            Add Step
          </Button>
        </div>
      </div>

      {/* Steps List */}
      <div className="space-y-2">
        {steps.map((step, index) => (
          <div key={index} className="border border-gray-200 rounded-lg">
            {/* Step Header */}
            <div
              className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
              onClick={() => setExpandedStep(expandedStep === index ? null : index)}
            >
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Step {index + 1}</span>
                <span className="text-sm text-gray-600 truncate max-w-32">
                  {step.title}
                </span>
                <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                  {step.type}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeStep(index);
                  }}
                >
                  <Minus className="w-4 h-4" />
                </Button>
                {expandedStep === index ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </div>
            </div>

            {/* Step Content Editor */}
            {expandedStep === index && (
              <div className="p-3 border-t border-gray-200 space-y-4">
                {/* Basic Step Settings */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title
                    </label>
                    <Input
                      value={step.title}
                      onChange={(e) => updateStep(index, { title: e.target.value })}
                      placeholder="Step title..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Icon
                    </label>
                    <select
                      value={typeof step.icon === 'string' ? step.icon : 'Brain'}
                      onChange={(e) => updateStepIcon(index, e.target.value as IconName)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {ICON_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Content Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content Type
                  </label>
                  <select
                    value={step.type}
                    onChange={(e) => {
                      const newType = e.target.value as StepConfig['type'];
                      let defaultData: any = '';

                      // Set appropriate default data for each type
                      switch (newType) {
                        case 'infoBox':
                          defaultData = { heading: '', lines: [''] };
                          break;
                        case 'bulletList':
                        case 'numberedList':
                          defaultData = [''];
                          break;
                        case 'grid':
                          defaultData = [{ title: '', content: '' }];
                          break;
                        case 'keyValueGrid':
                          defaultData = [{ key: '', value: '' }];
                          break;
                        case 'comparison':
                          defaultData = [{ label: '', before: '', after: '' }];
                          break;
                        case 'table':
                          defaultData = { headers: ['Header 1', 'Header 2'], rows: [['Cell 1', 'Cell 2']] };
                          break;
                        case 'scatterPlot':
                          defaultData = { data: [{ x: 10, y: 20, label: 'Point 1' }], width: 400, height: 300 };
                          break;
                        default:
                          defaultData = '';
                      }

                      updateStep(index, { type: newType, data: defaultData });
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {CONTENT_TYPE_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Content Editor */}
                {renderContentEditor(step, index)}
              </div>
            )}
          </div>
        ))}
      </div>

      {steps.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-2xl mb-2">📚</div>
          <p>No steps configured</p>
          <p className="text-sm">Click &quot;Add Step&quot; to get started</p>
        </div>
      )}

      {/* Preview Modal */}
      <MultiStepPreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        steps={steps}
      />
    </div>
  );
};

/**
 * Conditional Rules component - manages show/hide rules for components
 */

'use client';

import React, { useState } from 'react';
import { Button, Input, Card, CardHeader, CardTitle, CardContent } from '@learn-platform/shared-ui';
import { Plus, Trash2, Eye, EyeOff } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { useBuilderStore } from '../../store/builderStore';
import { ConditionalRule } from '../../types';

interface ConditionalRulesProps {
  componentId: string;
}

export const ConditionalRules: React.FC<ConditionalRulesProps> = ({ componentId }) => {
  const {
    getComponent,
    addConditionalRule,
    removeConditionalRule,
    updateConditionalRule,
  } = useBuilderStore();

  const component = getComponent(componentId);
  const [newRuleCondition, setNewRuleCondition] = useState('');

  if (!component) return null;

  const rules = component.conditionalRules || [];

  const handleAddRule = () => {
    if (newRuleCondition.trim()) {
      const newRule: ConditionalRule = {
        id: uuidv4(),
        condition: newRuleCondition.trim(),
        action: 'show',
      };
      addConditionalRule(componentId, newRule);
      setNewRuleCondition('');
    }
  };

  const handleUpdateRule = (ruleId: string, updates: Partial<ConditionalRule>) => {
    updateConditionalRule(componentId, ruleId, updates);
  };

  const handleDeleteRule = (ruleId: string) => {
    removeConditionalRule(componentId, ruleId);
  };

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle className="text-sm font-medium">Conditional Rules</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Existing rules */}
        {rules.map((rule) => (
          <div key={rule.id} className="p-3 border border-gray-200 rounded-lg space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-700">Rule</span>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDeleteRule(rule.id)}
                className="h-6 w-6 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
            
            <div>
              <label className="block text-xs text-gray-500 mb-1">Condition</label>
              <Input
                value={rule.condition}
                onChange={(e) => handleUpdateRule(rule.id, { condition: e.target.value })}
                placeholder="e.g., state.isVisible === true"
                className="text-xs"
              />
            </div>
            
            <div>
              <label className="block text-xs text-gray-500 mb-1">Action</label>
              <select
                value={rule.action}
                onChange={(e) => handleUpdateRule(rule.id, { action: e.target.value as 'show' | 'hide' | 'disable' })}
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="show">Show</option>
                <option value="hide">Hide</option>
                <option value="disable">Disable</option>
              </select>
            </div>
          </div>
        ))}

        {/* Add new rule */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Input
              value={newRuleCondition}
              onChange={(e) => setNewRuleCondition(e.target.value)}
              placeholder="Enter condition (e.g., state.isVisible === true)"
              className="text-xs"
              onKeyPress={(e) => e.key === 'Enter' && handleAddRule()}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddRule}
              disabled={!newRuleCondition.trim()}
              className="flex-shrink-0"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
          
          <p className="text-xs text-gray-500">
            💡 Use simple conditions like <code className="bg-gray-100 px-1 rounded">state.isVisible === true</code>
          </p>
        </div>

        {/* Info */}
        {rules.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            <Eye className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <p className="text-xs">No conditional rules set</p>
            <p className="text-xs">Add rules to control component visibility</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

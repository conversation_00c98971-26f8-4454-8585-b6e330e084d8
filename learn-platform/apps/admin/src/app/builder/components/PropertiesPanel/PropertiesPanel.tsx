/**
 * Properties Panel - allows editing of selected component properties
 */

'use client';

import React from 'react';
import { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@learn-platform/shared-ui';
import { Trash2, Copy } from 'lucide-react';
import { useBuilderStore } from '../../store/builderStore';
import { getAvailableProps, getComponentLabel } from '../../utils/componentFactory';
import { ComponentProps } from '../../types';
import { ConditionalRules } from './ConditionalRules';
import { MultiStepExplainEditor } from './MultiStepExplainEditor';

export const PropertiesPanel: React.FC = () => {
  const {
    selectedComponentId,
    getSelectedComponent,
    updateComponent,
    removeComponent,
  } = useBuilderStore();

  const selectedComponent = getSelectedComponent();

  if (!selectedComponent) {
    return (
      <Card className="w-80 h-fit">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Properties</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8">
            <div className="text-3xl mb-2">⚙️</div>
            <p className="text-sm">
              Select a component to edit its properties
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const availableProps = getAvailableProps(selectedComponent.type);
  const componentLabel = getComponentLabel(selectedComponent.type, selectedComponent.props);

  const handlePropChange = (propName: string, value: any) => {
    updateComponent(selectedComponent.id, {
      props: {
        ...selectedComponent.props,
        [propName]: value,
      },
    });
  };

  const handleSizeChange = (dimension: 'width' | 'height', value: number) => {
    updateComponent(selectedComponent.id, {
      size: {
        ...selectedComponent.size,
        [dimension]: Math.max(50, value), // Minimum size
      },
    });
  };

  const handlePositionChange = (axis: 'x' | 'y', value: number) => {
    updateComponent(selectedComponent.id, {
      position: {
        ...selectedComponent.position,
        [axis]: Math.max(0, value), // Minimum position
      },
    });
  };

  const handleDelete = () => {
    if (selectedComponentId) {
      removeComponent(selectedComponentId);
    }
  };

  const renderPropEditor = (propName: string, options: any[]) => {
    const currentValue = selectedComponent.props[propName as keyof ComponentProps];

    if (Array.isArray(options)) {
      // Dropdown for predefined options
      return (
        <select
          value={currentValue as string || ''}
          onChange={(e) => handlePropChange(propName, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {options.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      );
    }

    // Text input for other types
    return (
      <Input
        value={currentValue as string || ''}
        onChange={(e) => handlePropChange(propName, e.target.value)}
        placeholder={`Enter ${propName}...`}
      />
    );
  };

  return (
    <Card className="w-80 h-fit">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center justify-between">
          Properties
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handleDelete}
              className="h-8 w-8 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Component info */}
        <div className="pb-4 border-b border-gray-200">
          <h3 className="font-medium text-gray-900 mb-1">{componentLabel}</h3>
          <p className="text-xs text-gray-500">ID: {selectedComponent.id.slice(0, 8)}...</p>
        </div>

        {/* Position */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">X</label>
              <Input
                type="number"
                value={selectedComponent.position.x}
                onChange={(e) => handlePositionChange('x', parseInt(e.target.value) || 0)}
              />
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Y</label>
              <Input
                type="number"
                value={selectedComponent.position.y}
                onChange={(e) => handlePositionChange('y', parseInt(e.target.value) || 0)}
              />
            </div>
          </div>
        </div>

        {/* Size */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Width</label>
              <Input
                type="number"
                value={selectedComponent.size.width}
                onChange={(e) => handleSizeChange('width', parseInt(e.target.value) || 50)}
              />
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Height</label>
              <Input
                type="number"
                value={selectedComponent.size.height}
                onChange={(e) => handleSizeChange('height', parseInt(e.target.value) || 50)}
              />
            </div>
          </div>
        </div>

        {/* Component-specific properties */}
        {Object.entries(availableProps).map(([propName, options]) => (
          <div key={propName}>
            <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
              {propName.replace(/([A-Z])/g, ' $1').trim()}
            </label>
            {renderPropEditor(propName, options)}
          </div>
        ))}

        {/* Text content for text components */}
        {selectedComponent.type === 'text' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Text Content</label>
            <textarea
              value={selectedComponent.props.text || ''}
              onChange={(e) => handlePropChange('text', e.target.value)}
              placeholder="Enter text content..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
            />
          </div>
        )}

        {/* Button text for button components */}
        {selectedComponent.type === 'button' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Button Text</label>
            <Input
              value={selectedComponent.props.children as string || ''}
              onChange={(e) => handlePropChange('children', e.target.value)}
              placeholder="Button text..."
            />
          </div>
        )}

        {/* MultiStepExplain editor */}
        {selectedComponent.type === 'multiStepExplain' && (
          <div>
            <MultiStepExplainEditor
              steps={selectedComponent.props.steps || []}
              onChange={(steps) => handlePropChange('steps', steps)}
            />
          </div>
        )}

        {/* Conditional Rules */}
        <ConditionalRules componentId={selectedComponent.id} />
      </CardContent>
    </Card>
  );
};

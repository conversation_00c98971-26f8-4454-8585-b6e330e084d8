/**
 * Individual palette item component for dragging components to canvas
 */

'use client';

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { PaletteItem as PaletteItemType } from '../../types';

interface PaletteItemProps {
  item: PaletteItemType;
}

export const PaletteItem: React.FC<PaletteItemProps> = ({ item }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `palette-${item.type}`,
    data: {
      type: 'palette-item',
      componentType: item.type,
    },
  });



  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`
        flex flex-col items-center p-4 bg-white border-2 border-gray-200 rounded-lg
        cursor-grab active:cursor-grabbing hover:border-blue-300 hover:shadow-md
        transition-all duration-200 select-none
        ${isDragging ? 'opacity-50 shadow-lg' : ''}
      `}
    >
      {/* Icon */}
      <div className="text-2xl mb-2">
        {item.icon}
      </div>

      {/* Label */}
      <span className="text-sm font-medium text-gray-700">
        {item.label}
      </span>

      {/* Size indicator */}
      <span className="text-xs text-gray-500 mt-1">
        {item.defaultSize.width} × {item.defaultSize.height}
      </span>
    </div>
  );
};

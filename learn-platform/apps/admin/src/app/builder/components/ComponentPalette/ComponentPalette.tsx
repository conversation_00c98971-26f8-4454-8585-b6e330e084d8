/**
 * Component Palette - displays draggable components for the builder
 */

'use client';

import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@learn-platform/shared-ui';
import { PaletteItem } from './PaletteItem';
import { PALETTE_ITEMS } from '../../utils/componentFactory';

export const ComponentPalette: React.FC = () => {
  return (
    <Card className="w-64 h-fit">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Components</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {PALETTE_ITEMS.map((item) => (
            <PaletteItem key={item.type} item={item} />
          ))}
        </div>
        
        {/* Instructions */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-xs text-blue-700">
            💡 <strong>Tip:</strong> Drag components from here to the canvas to start building your interface.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

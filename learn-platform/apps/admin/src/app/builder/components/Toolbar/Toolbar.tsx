/**
 * Toolbar component - provides actions for save, load, clear, and canvas settings
 */

'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@learn-platform/shared-ui';
import {
  Save,
  FolderOpen,
  Trash2,
  Grid3X3,
  Eye,
  EyeOff,
  Settings,
  Download
} from 'lucide-react';
import { useBuilderStore } from '../../store/builderStore';

export const Toolbar: React.FC = () => {
  const {
    components,
    snapToGrid,
    showGrid,
    gridSize,
    saveCanvas,
    loadCanvas,
    clearCanvas,
    toggleSnapToGrid,
    toggleShowGrid,
    setGridSize,

  } = useBuilderStore();

  const handleClear = () => {
    if (components.length > 0) {
      const confirmed = window.confirm(
        'Are you sure you want to clear the canvas? This action cannot be undone.'
      );
      if (confirmed) {
        clearCanvas();
      }
    }
  };

  const handleExport = () => {
    // Simple JSON export for now
    const canvasData = {
      components,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    const blob = new Blob([JSON.stringify(canvasData, null, 2)], {
      type: 'application/json',
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `canvas-export-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };



  return (
    <Card className="p-4">
      <div className="flex flex-wrap items-center gap-3">
        {/* File operations */}
        <div className="flex items-center space-x-2 border-r border-gray-200 pr-3">
          <Button
            variant="outline"
            size="sm"
            onClick={saveCanvas}
            className="flex items-center"
          >
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={loadCanvas}
            className="flex items-center"
          >
            <FolderOpen className="h-4 w-4 mr-2" />
            Load
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>

        {/* Canvas operations */}
        <div className="flex items-center space-x-2 border-r border-gray-200 pr-3">
          <Button
            variant="destructive"
            size="sm"
            onClick={handleClear}
            className="flex items-center"
            disabled={components.length === 0}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear
          </Button>
        </div>

        {/* Grid settings */}
        <div className="flex items-center space-x-2 border-r border-gray-200 pr-3">
          <Button
            variant={snapToGrid ? "default" : "outline"}
            size="sm"
            onClick={toggleSnapToGrid}
            className="flex items-center"
          >
            <Grid3X3 className="h-4 w-4 mr-2" />
            Snap to Grid
          </Button>

          <Button
            variant={showGrid ? "default" : "outline"}
            size="sm"
            onClick={toggleShowGrid}
            className="flex items-center"
          >
            {showGrid ? (
              <Eye className="h-4 w-4 mr-2" />
            ) : (
              <EyeOff className="h-4 w-4 mr-2" />
            )}
            Grid
          </Button>

          <select
            value={gridSize}
            onChange={(e) => setGridSize(parseInt(e.target.value))}
            className="px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value={8}>8px</option>
            <option value={16}>16px</option>
            <option value={24}>24px</option>
            <option value={32}>32px</option>
          </select>
        </div>

        {/* Stats */}
        <div className="flex items-center text-sm text-gray-600">
          <span className="font-medium">{components.length}</span>
          <span className="ml-1">
            component{components.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>
    </Card>
  );
};

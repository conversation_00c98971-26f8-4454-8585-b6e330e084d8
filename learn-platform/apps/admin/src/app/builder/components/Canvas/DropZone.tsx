/**
 * Drop Zone component - handles dropping components onto the canvas
 */

'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Size } from '../../types';

interface DropZoneProps {
  canvasSize: Size;
  children: React.ReactNode;
}

export const DropZone: React.FC<DropZoneProps> = ({ canvasSize, children }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: 'canvas-drop-zone',
    data: {
      accepts: ['palette-item', 'canvas-component'],
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={`
        relative bg-white border-2 border-dashed border-gray-300 rounded-lg
        transition-all duration-200
        ${isOver ? 'border-blue-500 bg-blue-50' : ''}
      `}
      style={{
        width: canvasSize.width,
        height: canvasSize.height,
        minWidth: canvasSize.width,
        minHeight: canvasSize.height,
      }}
    >
      {children}
      
      {/* Drop indicator */}
      {isOver && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg">
            Drop component here
          </div>
        </div>
      )}
    </div>
  );
};

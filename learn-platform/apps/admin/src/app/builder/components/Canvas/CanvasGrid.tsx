/**
 * Canvas Grid component - renders the grid overlay for the builder canvas
 */

'use client';

import React from 'react';
import { Size } from '../../types';
import { getGridBackgroundStyle } from '../../utils/gridUtils';

interface CanvasGridProps {
  canvasSize: Size;
  gridSize: number;
  showGrid: boolean;
}

export const CanvasGrid: React.FC<CanvasGridProps> = ({
  canvasSize,
  gridSize,
  showGrid,
}) => {
  const gridStyle = getGridBackgroundStyle(gridSize, showGrid);

  return (
    <div
      className="absolute inset-0 pointer-events-none"
      style={{
        width: canvasSize.width,
        height: canvasSize.height,
        ...gridStyle,
      }}
    />
  );
};

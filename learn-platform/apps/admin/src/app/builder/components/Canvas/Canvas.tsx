/**
 * Main Canvas component - the central workspace for building components
 */

'use client';

import React from 'react';
import { useBuilderStore } from '../../store/builderStore';
import { CanvasGrid } from './CanvasGrid';
import { DropZone } from './DropZone';
import {
  RenderableButton,
  RenderableInput,
  RenderableText,
  RenderableContainer,
  RenderableMultiStepExplain
} from '../RenderedComponents';
import { ComponentInstance } from '../../types';

export const Canvas: React.FC = () => {
  const {
    components,
    selectedComponentId,
    canvasSize,
    gridSize,
    showGrid,
    selectComponent,
    updateComponent,
  } = useBuilderStore();

  const handleCanvasClick = (e: React.MouseEvent) => {
    // Deselect component when clicking on empty canvas
    if (e.target === e.currentTarget) {
      selectComponent(null);
    }
  };

  const renderComponent = (component: ComponentInstance) => {
    const isSelected = component.id === selectedComponentId;
    const commonProps = {
      component,
      isSelected,
      onSelect: () => selectComponent(component.id),
      onUpdate: (updates: Partial<ComponentInstance>) => updateComponent(component.id, updates),
    };

    switch (component.type) {
      case 'button':
        return <RenderableButton key={component.id} {...commonProps} />;
      case 'input':
        return <RenderableInput key={component.id} {...commonProps} />;
      case 'text':
        return <RenderableText key={component.id} {...commonProps} />;
      case 'container':
        return <RenderableContainer key={component.id} {...commonProps} />;
      case 'multiStepExplain':
        return <RenderableMultiStepExplain key={component.id} {...commonProps} />;
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 overflow-auto p-4 bg-gray-50">
      <div className="relative">
        <DropZone canvasSize={canvasSize}>
          {/* Grid overlay */}
          <CanvasGrid
            canvasSize={canvasSize}
            gridSize={gridSize}
            showGrid={showGrid}
          />

          {/* Canvas click handler */}
          <div
            className="absolute inset-0 z-0"
            onClick={handleCanvasClick}
          />

          {/* Rendered components */}
          {components.map(renderComponent)}

          {/* Empty state */}
          {components.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-4">🎨</div>
                <h3 className="text-lg font-medium mb-2">Start Building</h3>
                <p className="text-sm">
                  Drag components from the palette to begin creating your interface
                </p>
              </div>
            </div>
          )}
        </DropZone>
      </div>
    </div>
  );
};

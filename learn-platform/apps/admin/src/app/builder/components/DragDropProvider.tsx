/**
 * Drag and Drop Provider - wraps the entire builder interface with DndContext
 */

'use client';

import React from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import { useBuilderStore } from '../store/builderStore';
import { ComponentType, ComponentInstance } from '../types';
import {
  RenderableButton,
  RenderableInput,
  RenderableText,
  RenderableContainer
} from './RenderedComponents';

interface DragDropProviderProps {
  children: React.ReactNode;
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({ children }) => {
  const {
    components,
    selectedComponentId,
    addComponent,
    selectComponent,
    moveComponent,
    updateComponent,
  } = useBuilderStore();

  const [draggedComponent, setDraggedComponent] = React.useState<ComponentInstance | null>(null);
  const [draggedPaletteType, setDraggedPaletteType] = React.useState<ComponentType | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const data = active.data.current;

    console.log('Drag started:', { activeId: active.id, data });

    if (data?.type === 'canvas-component') {
      const component = components.find(c => c.id === data.componentId);
      if (component) {
        setDraggedComponent(component);
      }
    } else if (data?.type === 'palette-item') {
      setDraggedPaletteType(data.componentType);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over, delta } = event;

    console.log('Drag ended:', {
      activeId: active.id,
      overId: over?.id,
      activeData: active.data.current,
      overData: over?.data.current,
      delta
    });

    setDraggedComponent(null);
    setDraggedPaletteType(null);

    if (!over) {
      console.log('No drop target found');
      return;
    }

    const activeData = active.data.current;
    const overData = over.data.current;

    // Handle dropping palette items onto canvas
    if (activeData?.type === 'palette-item' && overData?.accepts?.includes('palette-item')) {
      const componentType = activeData.componentType as ComponentType;

      // Calculate drop position relative to the canvas
      let dropPosition = { x: 50, y: 50 }; // Default position

      // Try to get a more accurate drop position
      if (event.activatorEvent && over.rect) {
        const rect = over.rect;
        const mouseEvent = event.activatorEvent as MouseEvent;

        // Calculate position relative to the drop zone
        dropPosition = {
          x: Math.max(0, Math.min(mouseEvent.clientX - rect.left, rect.width - 100)),
          y: Math.max(0, Math.min(mouseEvent.clientY - rect.top, rect.height - 50)),
        };
      }

      console.log('Adding component:', { componentType, dropPosition, rect: over.rect });
      addComponent(componentType, dropPosition);
    }

    // Handle moving existing components
    if (activeData?.type === 'canvas-component' && overData?.accepts?.includes('canvas-component')) {
      const componentId = activeData.componentId;
      const component = components.find(c => c.id === componentId);

      if (component && delta) {
        const newPosition = {
          x: Math.max(0, component.position.x + delta.x),
          y: Math.max(0, component.position.y + delta.y),
        };
        console.log('Moving component:', { componentId, newPosition });
        moveComponent(componentId, newPosition);
      }
    }
  };

  const renderDragOverlay = () => {
    if (draggedComponent) {
      const isSelected = draggedComponent.id === selectedComponentId;
      const commonProps = {
        component: draggedComponent,
        isSelected,
        onSelect: () => selectComponent(draggedComponent.id),
        onUpdate: (updates: Partial<ComponentInstance>) => updateComponent(draggedComponent.id, updates),
      };

      switch (draggedComponent.type) {
        case 'button':
          return <RenderableButton {...commonProps} />;
        case 'input':
          return <RenderableInput {...commonProps} />;
        case 'text':
          return <RenderableText {...commonProps} />;
        case 'container':
          return <RenderableContainer {...commonProps} />;
        default:
          return null;
      }
    }

    if (draggedPaletteType) {
      // Show a preview of the component being dragged from palette
      return (
        <div className="bg-white border-2 border-blue-500 rounded-lg p-4 shadow-lg opacity-75">
          <span className="text-sm font-medium text-blue-700">
            {draggedPaletteType.charAt(0).toUpperCase() + draggedPaletteType.slice(1)}
          </span>
        </div>
      );
    }

    return null;
  };

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
      {children}

      {/* Drag overlay for visual feedback */}
      <DragOverlay>
        <div className="transform rotate-3 shadow-lg">
          {renderDragOverlay()}
        </div>
      </DragOverlay>
    </DndContext>
  );
};

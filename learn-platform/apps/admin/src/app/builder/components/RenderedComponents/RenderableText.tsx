/**
 * Renderable Text component for the Component Builder
 * Renders text elements with builder-specific functionality
 */

'use client';

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { ComponentInstance } from '../../types';

interface RenderableTextProps {
  component: ComponentInstance;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<ComponentInstance>) => void;
}

export const RenderableText: React.FC<RenderableTextProps> = ({
  component,
  isSelected,
  onSelect,
  onUpdate,
}) => {
  const { props, size, position } = component;
  const Tag = (props.tag || 'p') as keyof React.JSX.IntrinsicElements;

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `component-${component.id}`,
    data: {
      type: 'canvas-component',
      componentId: component.id,
    },
  });

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onSelect();
  };

  const dragStyle = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const getTextStyles = () => {
    const baseStyles = {
      margin: 0,
      padding: 0,
      width: '100%',
      height: '100%',
      overflow: 'hidden',
      display: 'flex',
      alignItems: 'center',
      ...props.style,
    };

    // Add tag-specific styles
    switch (props.tag) {
      case 'h1':
        return { ...baseStyles, fontSize: '2rem', fontWeight: 'bold' };
      case 'h2':
        return { ...baseStyles, fontSize: '1.5rem', fontWeight: 'bold' };
      case 'h3':
        return { ...baseStyles, fontSize: '1.25rem', fontWeight: 'bold' };
      case 'h4':
        return { ...baseStyles, fontSize: '1.125rem', fontWeight: 'bold' };
      case 'h5':
        return { ...baseStyles, fontSize: '1rem', fontWeight: 'bold' };
      case 'h6':
        return { ...baseStyles, fontSize: '0.875rem', fontWeight: 'bold' };
      default:
        return { ...baseStyles, fontSize: '1rem' };
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={`absolute cursor-pointer transition-all duration-200 ${
        isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
      } ${isDragging ? 'opacity-50' : ''}`}
      style={{
        left: position.x,
        top: position.y,
        width: size.width,
        height: size.height,
        zIndex: component.zIndex,
        ...dragStyle,
      }}
      onClick={handleClick}
      {...listeners}
      {...attributes}
    >
      <Tag
        className={`${props.className || ''}`}
        style={getTextStyles()}
      >
        {props.text || 'Sample text'}
      </Tag>

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded">
          Text ({props.tag || 'p'})
        </div>
      )}
    </div>
  );
};

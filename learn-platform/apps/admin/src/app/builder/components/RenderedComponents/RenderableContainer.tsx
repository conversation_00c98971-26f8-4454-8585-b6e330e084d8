/**
 * Renderable Container component for the Component Builder
 * Renders container elements with builder-specific functionality
 */

'use client';

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { ComponentInstance } from '../../types';

interface RenderableContainerProps {
  component: ComponentInstance;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<ComponentInstance>) => void;
}

export const RenderableContainer: React.FC<RenderableContainerProps> = ({
  component,
  isSelected,
  onSelect,
  onUpdate,
}) => {
  const { props, size, position } = component;

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `component-${component.id}`,
    data: {
      type: 'canvas-component',
      componentId: component.id,
    },
  });

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onSelect();
  };

  const dragStyle = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const containerStyles = {
    padding: props.padding || 16,
    backgroundColor: props.backgroundColor || '#f3f4f6',
    border: '2px dashed #d1d5db',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#6b7280',
    fontSize: '14px',
    fontWeight: '500',
    ...props.style,
  };

  return (
    <div
      ref={setNodeRef}
      className={`absolute cursor-pointer transition-all duration-200 ${
        isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
      } ${isDragging ? 'opacity-50' : ''}`}
      style={{
        left: position.x,
        top: position.y,
        width: size.width,
        height: size.height,
        zIndex: component.zIndex,
        ...dragStyle,
      }}
      onClick={handleClick}
      {...listeners}
      {...attributes}
    >
      <div
        className={`w-full h-full ${props.className || ''}`}
        style={containerStyles}
      >
        Container
        <br />
        <span className="text-xs opacity-75">
          {size.width} × {size.height}
        </span>
      </div>

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded">
          Container
        </div>
      )}
    </div>
  );
};

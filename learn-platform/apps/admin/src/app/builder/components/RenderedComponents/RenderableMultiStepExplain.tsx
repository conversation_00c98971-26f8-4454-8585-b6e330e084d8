/**
 * Renderable MultiStepExplain component for the Component Builder
 * Provides a builder-aware version with drag-and-drop support
 */

'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ComponentInstance } from '../../types';
import { MultiStepExplain } from '../../../../lib/components/templates';
import { getIconElement, IconName } from '../../utils/iconMapping';

interface RenderableMultiStepExplainProps {
  component: ComponentInstance;
  isSelected: boolean;
  onSelect: () => void;
}

export const RenderableMultiStepExplain: React.FC<RenderableMultiStepExplainProps> = ({
  component,
  isSelected,
  onSelect,
}) => {
  const { position, size, props } = component;

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: component.id,
    data: {
      type: 'canvas-component',
      componentId: component.id,
    },
  });

  const dragStyle = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect();
  };

  // Convert icon names back to React elements for rendering
  const processedSteps = props.steps?.map(step => ({
    ...step,
    icon: typeof step.icon === 'string'
      ? getIconElement(step.icon as IconName, "w-8 h-8 text-blue-500")
      : step.icon
  })) || [];

  // Show simplified preview in builder mode (first step only)
  const previewSteps = processedSteps.length > 0 ? [processedSteps[0]] : [];

  return (
    <div
      ref={setNodeRef}
      className={`absolute cursor-pointer transition-all duration-200 ${
        isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
      } ${isDragging ? 'opacity-50' : ''}`}
      style={{
        left: position.x,
        top: position.y,
        width: size.width,
        height: size.height,
        zIndex: component.zIndex,
        ...dragStyle,
      }}
      onClick={handleClick}
      {...listeners}
      {...attributes}
    >
      <div className="w-full h-full overflow-hidden rounded-lg border border-gray-200 bg-white">
        {previewSteps.length > 0 ? (
          <div className="p-4 h-full">
            {/* Header */}
            <div className="flex items-center justify-center mb-4">
              {previewSteps[0].icon}
              <h3 className="text-lg font-bold ml-2 text-gray-800 truncate">
                {previewSteps[0].title}
              </h3>
            </div>

            {/* Preview content */}
            <div className="text-sm text-gray-600 overflow-hidden">
              {previewSteps[0].type === 'paragraph' && (
                <p className="line-clamp-3">
                  {Array.isArray(previewSteps[0].data)
                    ? previewSteps[0].data[0]
                    : previewSteps[0].data}
                </p>
              )}
              {previewSteps[0].type === 'infoBox' && (
                <div className="bg-blue-50 p-2 rounded">
                  {previewSteps[0].data.heading && (
                    <p className="font-semibold text-blue-800 text-xs mb-1">
                      {previewSteps[0].data.heading}
                    </p>
                  )}
                  <p className="text-blue-700 text-xs line-clamp-2">
                    {previewSteps[0].data.lines?.[0]}
                  </p>
                </div>
              )}
              {(previewSteps[0].type === 'bulletList' || previewSteps[0].type === 'numberedList') && (
                <ul className="text-xs space-y-1">
                  {previewSteps[0].data.slice(0, 3).map((item: string, index: number) => (
                    <li key={index} className="flex items-start">
                      <span className="text-blue-500 mr-1">•</span>
                      <span className="line-clamp-1">{item}</span>
                    </li>
                  ))}
                </ul>
              )}
              {previewSteps[0].type === 'grid' && (
                <div className="grid grid-cols-2 gap-2">
                  {previewSteps[0].data.slice(0, 2).map((item: any, index: number) => (
                    <div key={index} className="bg-gray-50 p-2 rounded text-xs">
                      <p className="font-semibold truncate">{item.title}</p>
                      <p className="text-gray-600 line-clamp-2">{item.content}</p>
                    </div>
                  ))}
                </div>
              )}
              {/* Add more preview types as needed */}
            </div>

            {/* Step indicator */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
              <div className="flex space-x-1">
                {processedSteps.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full ${
                      index === 0 ? 'bg-blue-500' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* Builder mode indicator */}
            <div className="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
              Preview
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500 text-sm">
            <div className="text-center">
              <div className="text-2xl mb-2">📚</div>
              <p>Multi-Step Explain</p>
              <p className="text-xs">No steps configured</p>
            </div>
          </div>
        )}
      </div>

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded">
          Multi-Step Explain ({processedSteps.length} steps)
        </div>
      )}
    </div>
  );
};

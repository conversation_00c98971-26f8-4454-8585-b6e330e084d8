/**
 * Renderable Input component for the Component Builder
 * Wraps the shared UI Input with builder-specific functionality
 */

'use client';

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { Input } from '@learn-platform/shared-ui';
import { ComponentInstance } from '../../types';

interface RenderableInputProps {
  component: ComponentInstance;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<ComponentInstance>) => void;
}

export const RenderableInput: React.FC<RenderableInputProps> = ({
  component,
  isSelected,
  onSelect,
  onUpdate,
}) => {
  const { props, size, position } = component;

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `component-${component.id}`,
    data: {
      type: 'canvas-component',
      componentId: component.id,
    },
  });

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onSelect();
  };

  const dragStyle = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      className={`absolute cursor-pointer transition-all duration-200 ${
        isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
      } ${isDragging ? 'opacity-50' : ''}`}
      style={{
        left: position.x,
        top: position.y,
        width: size.width,
        height: size.height,
        zIndex: component.zIndex,
        ...dragStyle,
      }}
      onClick={handleClick}
      {...listeners}
      {...attributes}
    >
      <Input
        type={props.type}
        placeholder={props.placeholder}
        value={props.value || ''}
        className={`w-full h-full ${props.className || ''}`}
        style={props.style}
        readOnly // Prevent editing in builder mode
      />

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded">
          Input
        </div>
      )}
    </div>
  );
};

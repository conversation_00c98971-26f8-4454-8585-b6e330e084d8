/**
 * TypeScript type definitions for the Component Builder Interface
 * Defines the core data structures used throughout the builder
 */

import { StepConfig } from '../../lib/components/templates/types';

export type ComponentType = 'button' | 'input' | 'text' | 'container' | 'multiStepExplain';

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface ComponentProps {
  // Common props for all components
  className?: string;
  style?: React.CSSProperties;

  // Button specific props
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children?: string; // Button text content

  // Input specific props
  type?: string;
  placeholder?: string;
  value?: string;

  // Text specific props
  text?: string;
  tag?: 'p' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'span';

  // Container specific props
  padding?: number;
  backgroundColor?: string;

  // MultiStepExplain specific props
  steps?: StepConfig[];
}

export interface ConditionalRule {
  id: string;
  condition: string; // Simple condition like "state.isVisible === true"
  action: 'show' | 'hide' | 'disable';
}

export interface ComponentInstance {
  id: string;
  type: ComponentType;
  position: Position;
  size: Size;
  props: ComponentProps;
  conditionalRules?: ConditionalRule[];
  zIndex: number;
}

export interface CanvasState {
  components: ComponentInstance[];
  selectedComponentId: string | null;
  canvasSize: Size;
  gridSize: number;
  snapToGrid: boolean;
  showGrid: boolean;
}

export interface BuilderState extends CanvasState {
  // Actions for component management
  addComponent: (type: ComponentType, position: Position) => void;
  removeComponent: (id: string) => void;
  updateComponent: (id: string, updates: Partial<ComponentInstance>) => void;
  selectComponent: (id: string | null) => void;
  moveComponent: (id: string, position: Position) => void;
  resizeComponent: (id: string, size: Size) => void;

  // Actions for canvas settings
  setGridSize: (size: number) => void;
  toggleSnapToGrid: () => void;
  toggleShowGrid: () => void;
  setCanvasSize: (size: Size) => void;

  // Actions for conditional rules
  addConditionalRule: (componentId: string, rule: ConditionalRule) => void;
  removeConditionalRule: (componentId: string, ruleId: string) => void;
  updateConditionalRule: (componentId: string, ruleId: string, updates: Partial<ConditionalRule>) => void;

  // Persistence actions
  saveCanvas: () => void;
  loadCanvas: () => void;
  clearCanvas: () => void;

  // Utility actions
  getComponent: (id: string) => ComponentInstance | undefined;
  getSelectedComponent: () => ComponentInstance | undefined;
}

export interface DragData {
  type: 'palette-item' | 'canvas-component';
  componentType?: ComponentType;
  componentId?: string;
}

export interface PaletteItem {
  type: ComponentType;
  label: string;
  icon: string;
  defaultProps: ComponentProps;
  defaultSize: Size;
}

// Grid and positioning utilities
export interface GridConfig {
  size: number;
  showGrid: boolean;
  snapToGrid: boolean;
}

export interface DropZoneData {
  id: string;
  accepts: string[];
}

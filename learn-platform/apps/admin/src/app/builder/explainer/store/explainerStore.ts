/**
 * Zustand store for Explainer Builder state management
 */

import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import {
  ExplainerBuilderState,
  Explainer,
  ExplainerStep,
  ContentBlock,
  DEFAULT_BLOCK_DATA
} from '../types';
import { StepConfig } from '../../../../lib/components/templates/types';

const createDefaultStep = (title = 'New Step'): ExplainerStep => ({
  id: uuidv4(),
  title,
  icon: 'BookOpen',
  blocks: []
});

const createDefaultExplainer = (name: string): Explainer => ({
  id: uuidv4(),
  name,
  description: '',
  steps: [createDefaultStep('Introduction')],
  createdAt: new Date(),
  updatedAt: new Date()
});

export const useExplainerStore = create<ExplainerBuilderState>((set, get) => ({
  // Initial state
  explainer: null,
  currentStepIndex: 0,
  editingBlockId: null,
  isPreviewMode: false,
  isSaving: false,

  // Explainer management
  createNewExplainer: (name: string) => {
    const newExplainer = createDefaultExplainer(name);
    set({
      explainer: newExplainer,
      currentStepIndex: 0,
      editingBlockId: null,
      isPreviewMode: false
    });
  },

  loadExplainer: (explainer: Explainer) => {
    set({
      explainer,
      currentStepIndex: 0,
      editingBlockId: null,
      isPreviewMode: false
    });
  },

  updateExplainerInfo: (updates) => {
    const { explainer } = get();
    if (!explainer) return;

    set({
      explainer: {
        ...explainer,
        ...updates,
        updatedAt: new Date()
      }
    });
  },

  // Step management
  addStep: () => {
    const { explainer } = get();
    if (!explainer) return;

    const newStep = createDefaultStep(`Step ${explainer.steps.length + 1}`);
    set({
      explainer: {
        ...explainer,
        steps: [...explainer.steps, newStep],
        updatedAt: new Date()
      },
      currentStepIndex: explainer.steps.length // Switch to new step
    });
  },

  updateStep: (stepId: string, updates) => {
    const { explainer } = get();
    if (!explainer) return;

    set({
      explainer: {
        ...explainer,
        steps: explainer.steps.map(step =>
          step.id === stepId ? { ...step, ...updates } : step
        ),
        updatedAt: new Date()
      }
    });
  },

  deleteStep: (stepId: string) => {
    const { explainer, currentStepIndex } = get();
    if (!explainer || explainer.steps.length <= 1) return; // Don't delete last step

    const stepIndex = explainer.steps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) return;

    const newSteps = explainer.steps.filter(step => step.id !== stepId);
    const newCurrentIndex = currentStepIndex >= newSteps.length
      ? newSteps.length - 1
      : currentStepIndex;

    set({
      explainer: {
        ...explainer,
        steps: newSteps,
        updatedAt: new Date()
      },
      currentStepIndex: newCurrentIndex,
      editingBlockId: null
    });
  },

  setCurrentStep: (index: number) => {
    const { explainer } = get();
    if (!explainer || index < 0 || index >= explainer.steps.length) return;

    set({
      currentStepIndex: index,
      editingBlockId: null // Clear editing when switching steps
    });
  },

  reorderSteps: (fromIndex: number, toIndex: number) => {
    const { explainer } = get();
    if (!explainer) return;

    const newSteps = [...explainer.steps];
    const [movedStep] = newSteps.splice(fromIndex, 1);
    newSteps.splice(toIndex, 0, movedStep);

    set({
      explainer: {
        ...explainer,
        steps: newSteps,
        updatedAt: new Date()
      },
      currentStepIndex: toIndex
    });
  },

  // Block management
  addBlock: (type: ContentBlock['type'], stepId?: string) => {
    const { explainer, currentStepIndex } = get();
    if (!explainer) return;

    const targetStepId = stepId || explainer.steps[currentStepIndex]?.id;
    if (!targetStepId) return;

    const newBlock: ContentBlock = {
      id: uuidv4(),
      type,
      data: DEFAULT_BLOCK_DATA[type],
      isEditing: true // Start in editing mode
    };

    set({
      explainer: {
        ...explainer,
        steps: explainer.steps.map(step =>
          step.id === targetStepId
            ? { ...step, blocks: [...step.blocks, newBlock] }
            : step
        ),
        updatedAt: new Date()
      },
      editingBlockId: newBlock.id
    });
  },

  updateBlock: (blockId: string, data: any) => {
    const { explainer } = get();
    if (!explainer) return;

    set({
      explainer: {
        ...explainer,
        steps: explainer.steps.map(step => ({
          ...step,
          blocks: step.blocks.map(block =>
            block.id === blockId ? { ...block, data } : block
          )
        })),
        updatedAt: new Date()
      }
    });
  },

  deleteBlock: (blockId: string) => {
    const { explainer, editingBlockId } = get();
    if (!explainer) return;

    set({
      explainer: {
        ...explainer,
        steps: explainer.steps.map(step => ({
          ...step,
          blocks: step.blocks.filter(block => block.id !== blockId)
        })),
        updatedAt: new Date()
      },
      editingBlockId: editingBlockId === blockId ? null : editingBlockId
    });
  },

  reorderBlocks: (stepId: string, fromIndex: number, toIndex: number) => {
    const { explainer } = get();
    if (!explainer) return;

    set({
      explainer: {
        ...explainer,
        steps: explainer.steps.map(step => {
          if (step.id !== stepId) return step;

          const newBlocks = [...step.blocks];
          const [movedBlock] = newBlocks.splice(fromIndex, 1);
          newBlocks.splice(toIndex, 0, movedBlock);

          return { ...step, blocks: newBlocks };
        }),
        updatedAt: new Date()
      }
    });
  },

  // Editing state
  setEditingBlock: (blockId: string | null) => {
    set({ editingBlockId: blockId });
  },

  // Preview and save
  togglePreview: () => {
    set(state => ({
      isPreviewMode: !state.isPreviewMode,
      editingBlockId: null // Clear editing in preview mode
    }));
  },

  saveExplainer: async () => {
    const { explainer } = get();
    if (!explainer) return;

    set({ isSaving: true });

    try {
      // Note: This will be called from components that have access to tRPC
      // The actual API call will be handled by the component using tRPC mutations
      console.log('Saving explainer:', explainer);

      set({
        explainer: {
          ...explainer,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to save explainer:', error);
    } finally {
      set({ isSaving: false });
    }
  },

  exportToStepConfig: (): StepConfig[] => {
    const { explainer } = get();
    if (!explainer) return [];

    return explainer.steps.map(step => ({
      title: step.title,
      icon: step.icon, // Will be converted to ReactNode by the consuming component
      type: step.blocks.length > 0 ? step.blocks[0].type : 'paragraph',
      data: step.blocks.length > 0 ? step.blocks[0].data : 'No content'
    }));
  },

  // Utility functions
  getCurrentStep: () => {
    const { explainer, currentStepIndex } = get();
    return explainer?.steps[currentStepIndex] || null;
  },

  getBlockById: (blockId: string) => {
    const { explainer } = get();
    if (!explainer) return null;

    for (const step of explainer.steps) {
      const block = step.blocks.find(b => b.id === blockId);
      if (block) {
        return { block, stepId: step.id };
      }
    }
    return null;
  }
}));

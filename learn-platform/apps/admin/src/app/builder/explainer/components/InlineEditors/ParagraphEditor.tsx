/**
 * Paragraph Editor - Inline editor for paragraph content
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Textarea } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2 } from 'lucide-react';

interface ParagraphEditorProps {
  data: string | string[];
  onSave: (data: string[]) => void;
  onCancel: () => void;
}

export const ParagraphEditor: React.FC<ParagraphEditorProps> = ({
  data,
  onSave,
  onCancel
}) => {
  const [paragraphs, setParagraphs] = useState<string[]>(() => {
    if (Array.isArray(data)) return data;
    return [data || ''];
  });

  const handleParagraphChange = (index: number, value: string) => {
    const newParagraphs = [...paragraphs];
    newParagraphs[index] = value;
    setParagraphs(newParagraphs);
  };

  const addParagraph = () => {
    setParagraphs([...paragraphs, '']);
  };

  const removeParagraph = (index: number) => {
    if (paragraphs.length <= 1) return;
    const newParagraphs = paragraphs.filter((_, i) => i !== index);
    setParagraphs(newParagraphs);
  };

  const handleSave = () => {
    const filteredParagraphs = paragraphs.filter(p => p.trim() !== '');
    if (filteredParagraphs.length === 0) {
      filteredParagraphs.push('Enter your text here...');
    }
    onSave(filteredParagraphs);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  return (
    <div className="space-y-3">
      {paragraphs.map((paragraph, index) => (
        <div key={index} className="flex gap-2">
          <Textarea
            value={paragraph}
            onChange={(e) => handleParagraphChange(index, e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={`Paragraph ${index + 1}...`}
            className="flex-1 min-h-[80px] resize-none"
            autoFocus={index === 0}
          />
          {paragraphs.length > 1 && (
            <Button
              variant="ghost"
              size="sm"
              className="w-8 h-8 p-0 text-red-500 hover:text-red-700 self-start mt-2"
              onClick={() => removeParagraph(index)}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      ))}

      <div className="flex items-center justify-between pt-2">
        <Button
          variant="outline"
          size="sm"
          onClick={addParagraph}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Paragraph
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            className="flex items-center gap-2"
          >
            <Check className="w-4 h-4" />
            Save
          </Button>
        </div>
      </div>

      <div className="text-xs text-gray-500 mt-2">
        Press Ctrl/Cmd + Enter to save, Escape to cancel
      </div>
    </div>
  );
};

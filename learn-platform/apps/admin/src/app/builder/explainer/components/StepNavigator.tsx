/**
 * Step Navigator - Tabs for switching between steps and managing step configuration
 */

'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@learn-platform/shared-ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@learn-platform/shared-ui';
import { Plus, Edit2, Trash2, GripVertical } from 'lucide-react';
import { useExplainerStore } from '../store/explainerStore';
import { AVAILABLE_ICONS, AvailableIcon } from '../types';
import { getIconByName } from '../../utils/iconMapping';

export const StepNavigator: React.FC = () => {
  const {
    explainer,
    currentStepIndex,
    setCurrentStep,
    addStep,
    updateStep,
    deleteStep
  } = useExplainerStore();

  const [editingStep, setEditingStep] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [editIcon, setEditIcon] = useState<AvailableIcon>('BookOpen');

  if (!explainer) return null;

  const handleEditStep = (stepId: string) => {
    const step = explainer.steps.find(s => s.id === stepId);
    if (!step) return;

    setEditTitle(step.title);
    setEditIcon(step.icon as AvailableIcon);
    setEditingStep(stepId);
  };

  const handleSaveStep = () => {
    if (!editingStep) return;

    updateStep(editingStep, {
      title: editTitle,
      icon: editIcon
    });

    setEditingStep(null);
    setEditTitle('');
    setEditIcon('BookOpen');
  };

  const handleDeleteStep = (stepId: string) => {
    if (explainer.steps.length <= 1) return; // Don't delete last step
    deleteStep(stepId);
  };

  return (
    <div className="border-b border-gray-200 bg-white">
      <div className="flex items-center justify-between px-6 py-4">
        <h2 className="text-lg font-semibold text-gray-900">
          Steps ({explainer.steps.length})
        </h2>
        <Button
          onClick={addStep}
          size="sm"
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Step
        </Button>
      </div>

      <div className="flex items-center gap-2 px-6 pb-4 overflow-x-auto">
        {explainer.steps.map((step, index) => {
          const IconComponent = getIconByName(step.icon as AvailableIcon);
          const isActive = index === currentStepIndex;

          return (
            <div
              key={step.id}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-lg border cursor-pointer
                transition-all duration-200 min-w-fit
                ${isActive
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }
              `}
              onClick={() => setCurrentStep(index)}
            >
              <GripVertical className="w-4 h-4 text-gray-400" />

              {IconComponent && <IconComponent className="w-4 h-4" />}

              <span className="font-medium text-sm">
                {step.title}
              </span>

              <div className="flex items-center gap-1 ml-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-6 h-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditStep(step.id);
                      }}
                    >
                      <Edit2 className="w-3 h-3" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Edit Step</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          Step Title
                        </label>
                        <Input
                          value={editTitle}
                          onChange={(e) => setEditTitle(e.target.value)}
                          placeholder="Enter step title"
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          Icon
                        </label>
                        <Select
                          value={editIcon}
                          onValueChange={(value) => setEditIcon(value as AvailableIcon)}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {AVAILABLE_ICONS.map((iconName) => {
                              const Icon = getIconByName(iconName as AvailableIcon);
                              return (
                                <SelectItem key={iconName} value={iconName}>
                                  <div className="flex items-center gap-2">
                                    {Icon && <Icon className="w-4 h-4" />}
                                    {iconName}
                                  </div>
                                </SelectItem>
                              );
                            })}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={() => setEditingStep(null)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleSaveStep}>
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                {explainer.steps.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-6 h-6 p-0 text-red-500 hover:text-red-700"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteStep(step.id);
                    }}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

/**
 * Table Editor - Inline editor for table content
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2 } from 'lucide-react';
import { TableData } from '../../../../../lib/components/templates/types';

interface TableEditorProps {
  data: TableData;
  onSave: (data: TableData) => void;
  onCancel: () => void;
}

export const TableEditor: React.FC<TableEditorProps> = ({
  data,
  onSave,
  onCancel
}) => {
  const [headers, setHeaders] = useState<string[]>(data.headers || ['Column 1', 'Column 2']);
  const [rows, setRows] = useState<(string | number)[][]>(
    data.rows || [['Row 1 Col 1', 'Row 1 Col 2']]
  );

  const handleHeaderChange = (index: number, value: string) => {
    const newHeaders = [...headers];
    newHeaders[index] = value;
    setHeaders(newHeaders);
  };

  const handleCellChange = (rowIndex: number, colIndex: number, value: string) => {
    const newRows = [...rows];
    newRows[rowIndex] = [...newRows[rowIndex]];
    newRows[rowIndex][colIndex] = value;
    setRows(newRows);
  };

  const addColumn = () => {
    setHeaders([...headers, `Column ${headers.length + 1}`]);
    setRows(rows.map(row => [...row, '']));
  };

  const removeColumn = (index: number) => {
    if (headers.length <= 1) return;
    setHeaders(headers.filter((_, i) => i !== index));
    setRows(rows.map(row => row.filter((_, i) => i !== index)));
  };

  const addRow = () => {
    setRows([...rows, new Array(headers.length).fill('')]);
  };

  const removeRow = (index: number) => {
    if (rows.length <= 1) return;
    setRows(rows.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    onSave({ headers, rows });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Table Data</h4>
        <span className="text-xs text-gray-500">
          {headers.length} columns × {rows.length} rows
        </span>
      </div>

      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              {headers.map((header, index) => (
                <th key={index} className="p-2 border-r border-gray-200 last:border-r-0">
                  <div className="flex items-center gap-1">
                    <Input
                      value={header}
                      onChange={(e) => handleHeaderChange(index, e.target.value)}
                      className="text-center font-medium"
                      placeholder={`Column ${index + 1}`}
                    />
                    {headers.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-6 h-6 p-0 text-red-500"
                        onClick={() => removeColumn(index)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </th>
              ))}
              <th className="p-2 w-10">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-6 h-6 p-0"
                  onClick={addColumn}
                >
                  <Plus className="w-3 h-3" />
                </Button>
              </th>
            </tr>
          </thead>
          <tbody>
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex} className="border-t border-gray-200">
                {row.map((cell, colIndex) => (
                  <td key={colIndex} className="p-2 border-r border-gray-200 last:border-r-0">
                    <Input
                      value={cell.toString()}
                      onChange={(e) => handleCellChange(rowIndex, colIndex, e.target.value)}
                      placeholder={`R${rowIndex + 1}C${colIndex + 1}`}
                    />
                  </td>
                ))}
                <td className="p-2 w-10">
                  {rows.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-6 h-6 p-0 text-red-500"
                      onClick={() => removeRow(rowIndex)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={addRow}
        className="flex items-center gap-2"
      >
        <Plus className="w-4 h-4" />
        Add Row
      </Button>

      <div className="flex items-center justify-end gap-2 pt-2 border-t border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="flex items-center gap-2"
        >
          <X className="w-4 h-4" />
          Cancel
        </Button>
        <Button
          size="sm"
          onClick={handleSave}
          className="flex items-center gap-2"
        >
          <Check className="w-4 h-4" />
          Save
        </Button>
      </div>
    </div>
  );
};

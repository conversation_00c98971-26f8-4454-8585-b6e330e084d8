/**
 * Template List Component - Shows all user templates with CRUD operations
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@learn-platform/shared-ui';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@learn-platform/shared-ui';
import {
  Plus,
  FileText,
  Calendar,
  Layers,
  Trash2,
  Edit3,
  Loader2,
  Search
} from 'lucide-react';

import { trpc } from '../../../../lib/trpc';

interface TemplateListItem {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  stepCount: number;
}

interface TemplateCardProps {
  template: TemplateListItem;
  onDelete: (id: string) => void;
  isDeleting: boolean;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onDelete, isDeleting }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {template.name}
          </h3>
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {template.description || 'No description provided'}
          </p>
        </div>
        <div className="flex items-center gap-2 ml-4">
          <Link href={`/builder/explainer/${template.id}`}>
            <Button variant="outline" size="sm">
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
          </Link>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" size="sm" disabled={isDeleting}>
                {isDeleting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Template</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete &quot;{template.name}&quot;? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onDelete(template.id)}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Layers className="w-4 h-4" />
            <span>{template.stepCount} steps</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            <span>Updated {formatDate(template.updatedAt)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export const TemplateList: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [deletingTemplateId, setDeletingTemplateId] = useState<string | null>(null);



  // Fetch templates
  const {
    data: templatesData,
    isLoading,
    refetch
  } = trpc.templates.getAll.useQuery();

  // Delete template mutation
  const deleteTemplateMutation = trpc.templates.delete.useMutation({
    onSuccess: () => {
      refetch();
      setDeletingTemplateId(null);
    },
    onError: (error) => {
      console.error('Failed to delete template:', error);
      setDeletingTemplateId(null);
    },
  });

  // Create template mutation
  const createTemplateMutation = trpc.templates.create.useMutation({
    onSuccess: (data) => {
      if (data.success && data.template) {
        // Navigate to the new template editor
        window.location.href = `/builder/explainer/${data.template.id}`;
      }
    },
    onError: (error) => {
      console.error('Failed to create template:', error);
    },
  });

  const templates = templatesData?.templates || [];

  // Filter templates based on search query
  const filteredTemplates = templates.filter((template: TemplateListItem) =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateTemplate = () => {
    if (!newTemplateName.trim()) return;

    // Create a basic template structure
    const defaultStep = {
      id: crypto.randomUUID(),
      title: 'Introduction',
      icon: 'BookOpen',
      blocks: [],
    };

    createTemplateMutation.mutate({
      name: newTemplateName.trim(),
      description: '',
      steps: [defaultStep],
    });

    setNewTemplateName('');
    setShowCreateDialog(false);
  };

  const handleDeleteTemplate = (templateId: string) => {
    setDeletingTemplateId(templateId);
    deleteTemplateMutation.mutate({ id: templateId });
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Loading Templates
          </h3>
          <p className="text-gray-600">
            Please wait while we fetch your templates...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="w-6 h-6 mr-2 text-blue-600" />
              Explainer Templates
            </h1>
            <p className="text-gray-600">
              Manage your interactive step-by-step explanation templates
            </p>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create New Template
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Template</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Template Name
                  </label>
                  <Input
                    value={newTemplateName}
                    onChange={(e) => setNewTemplateName(e.target.value)}
                    placeholder="Enter template name..."
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleCreateTemplate();
                      }
                    }}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateTemplate}
                    disabled={!newTemplateName.trim() || createTemplateMutation.isPending}
                  >
                    {createTemplateMutation.isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Create Template'
                    )}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Templates Grid */}
      <div className="flex-1 overflow-y-auto">
        {filteredTemplates.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-6">📝</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {templates.length === 0 ? 'No Templates Yet' : 'No Matching Templates'}
              </h2>
              <p className="text-gray-600 mb-6 max-w-md">
                {templates.length === 0
                  ? 'Create your first explainer template to get started with building interactive step-by-step explanations.'
                  : 'Try adjusting your search query to find the templates you\'re looking for.'
                }
              </p>
              {templates.length === 0 && (
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Template
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template: TemplateListItem) => (
              <TemplateCard
                key={template.id}
                template={template}
                onDelete={handleDeleteTemplate}
                isDeleting={deletingTemplateId === template.id}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

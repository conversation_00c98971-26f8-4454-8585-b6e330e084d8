/**
 * List Editor - Inline editor for bullet and numbered lists
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2, GripVertical } from 'lucide-react';

interface ListEditorProps {
  data: string[];
  type: 'bulletList' | 'numberedList';
  onSave: (data: string[]) => void;
  onCancel: () => void;
}

export const ListEditor: React.FC<ListEditorProps> = ({
  data,
  type,
  onSave,
  onCancel
}) => {
  const [items, setItems] = useState<string[]>(data || ['']);

  const handleItemChange = (index: number, value: string) => {
    const newItems = [...items];
    newItems[index] = value;
    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, '']);
  };

  const removeItem = (index: number) => {
    if (items.length <= 1) return;
    const newItems = items.filter((_, i) => i !== index);
    setItems(newItems);
  };

  const moveItem = (fromIndex: number, toIndex: number) => {
    const newItems = [...items];
    const [movedItem] = newItems.splice(fromIndex, 1);
    newItems.splice(toIndex, 0, movedItem);
    setItems(newItems);
  };

  const handleSave = () => {
    const filteredItems = items.filter(item => item.trim() !== '');
    if (filteredItems.length === 0) {
      filteredItems.push('List item');
    }
    onSave(filteredItems);
  };

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (e.metaKey || e.ctrlKey) {
        handleSave();
      } else {
        // Add new item after current one
        const newItems = [...items];
        newItems.splice(index + 1, 0, '');
        setItems(newItems);
        // Focus will be handled by React
        setTimeout(() => {
          const nextInput = document.querySelector(`input[data-index="${index + 1}"]`) as HTMLInputElement;
          if (nextInput) nextInput.focus();
        }, 0);
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    } else if (e.key === 'Backspace' && items[index] === '' && items.length > 1) {
      e.preventDefault();
      removeItem(index);
      // Focus previous item
      setTimeout(() => {
        const prevInput = document.querySelector(`input[data-index="${Math.max(0, index - 1)}"]`) as HTMLInputElement;
        if (prevInput) prevInput.focus();
      }, 0);
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">
          {type === 'bulletList' ? 'Bullet List' : 'Numbered List'} Items
        </h4>
        <span className="text-xs text-gray-500">
          {items.length} item{items.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="space-y-2">
        {items.map((item, index) => (
          <div key={index} className="flex items-center gap-2 group">
            <div className="flex items-center gap-2 flex-1">
              <div className="flex items-center gap-2 text-gray-400">
                <GripVertical className="w-4 h-4 cursor-grab" />
                <span className="text-sm min-w-[20px]">
                  {type === 'bulletList' ? '•' : `${index + 1}.`}
                </span>
              </div>

              <Input
                data-index={index}
                value={item}
                onChange={(e) => handleItemChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                placeholder={`Item ${index + 1}...`}
                className="flex-1"
                autoFocus={index === 0}
              />
            </div>

            {items.length > 1 && (
              <Button
                variant="ghost"
                size="sm"
                className="w-8 h-8 p-0 text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => removeItem(index)}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between pt-2">
        <Button
          variant="outline"
          size="sm"
          onClick={addItem}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Item
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            className="flex items-center gap-2"
          >
            <Check className="w-4 h-4" />
            Save
          </Button>
        </div>
      </div>

      <div className="text-xs text-gray-500 space-y-1">
        <div>Press Enter to add new item, Ctrl/Cmd + Enter to save</div>
        <div>Backspace on empty item to delete, Escape to cancel</div>
      </div>
    </div>
  );
};

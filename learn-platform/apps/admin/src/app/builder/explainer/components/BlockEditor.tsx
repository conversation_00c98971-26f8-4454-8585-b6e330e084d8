/**
 * Block Editor - Linear editor for content blocks with drag-and-drop reordering
 */

'use client';

import React from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from '@dnd-kit/modifiers';

import { useExplainerStore } from '../store/explainerStore';
import { ContentBlock } from './ContentBlock';

export const BlockEditor: React.FC = () => {
  const {
    getCurrentStep,
    reorderBlocks
  } = useExplainerStore();

  const currentStep = getCurrentStep();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || !currentStep) return;

    const activeIndex = currentStep.blocks.findIndex(block => block.id === active.id);
    const overIndex = currentStep.blocks.findIndex(block => block.id === over.id);

    if (activeIndex !== overIndex) {
      reorderBlocks(currentStep.id, activeIndex, overIndex);
    }
  };

  if (!currentStep) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center text-gray-500">
          <div className="text-4xl mb-4">📝</div>
          <h3 className="text-lg font-medium mb-2">No Step Selected</h3>
          <p className="text-sm">
            Select a step from the navigation above to start editing
          </p>
        </div>
      </div>
    );
  }

  if (currentStep.blocks.length === 0) {
    return (
      <div className="flex-1 bg-white">
        <div className="max-w-4xl mx-auto p-8">
          <div className="text-center text-gray-500 py-16">
            <div className="text-4xl mb-4">✨</div>
            <h3 className="text-lg font-medium mb-2">Start Adding Content</h3>
            <p className="text-sm mb-4">
              Use the content type selector on the left to add blocks to &quot;{currentStep.title}&quot;
            </p>
            <div className="text-xs text-gray-400">
              Click any content type to add it instantly
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-gray-50 overflow-y-auto">
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {currentStep.title}
          </h2>
          <p className="text-sm text-gray-600">
            {currentStep.blocks.length} content block{currentStep.blocks.length !== 1 ? 's' : ''}
          </p>
        </div>

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis, restrictToParentElement]}
        >
          <SortableContext
            items={currentStep.blocks.map(block => block.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {currentStep.blocks.map((block, index) => (
                <ContentBlock
                  key={block.id}
                  block={block}
                  stepId={currentStep.id}
                  index={index}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        {/* Add content hint */}
        <div className="mt-8 p-4 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500">
          <p className="text-sm">
            Add more content blocks using the sidebar on the left
          </p>
        </div>
      </div>
    </div>
  );
};

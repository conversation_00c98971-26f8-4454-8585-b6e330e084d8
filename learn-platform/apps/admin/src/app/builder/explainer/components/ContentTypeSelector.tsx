/**
 * Content Type Selector - Sidebar for adding different content types to steps
 */

'use client';

import React from 'react';
import { Button } from '@learn-platform/shared-ui';
import {
  Type,
  Info,
  List,
  ListOrdered,
  Grid3X3,
  ArrowLeftRight,
  Table,
  ChartScatter,
  Hash
} from 'lucide-react';
import { useExplainerStore } from '../store/explainerStore';
import { ContentBlock } from '../types';

interface ContentTypeOption {
  type: ContentBlock['type'];
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const CONTENT_TYPES: ContentTypeOption[] = [
  {
    type: 'paragraph',
    label: 'Paragraph',
    description: 'Add text content',
    icon: Type
  },
  {
    type: 'infoBox',
    label: 'Info Box',
    description: 'Highlighted information',
    icon: Info
  },
  {
    type: 'bulletList',
    label: 'Bullet List',
    description: 'Unordered list items',
    icon: List
  },
  {
    type: 'numberedList',
    label: 'Numbered List',
    description: 'Ordered list items',
    icon: ListOrdered
  },
  {
    type: 'grid',
    label: 'Grid',
    description: 'Grid of cards',
    icon: Grid3X3
  },
  {
    type: 'comparison',
    label: 'Comparison',
    description: 'Before/after comparison',
    icon: ArrowLeftRight
  },
  {
    type: 'table',
    label: 'Table',
    description: 'Data table',
    icon: Table
  },
  {
    type: 'scatterPlot',
    label: 'Scatter Plot',
    description: 'Data visualization',
    icon: ChartScatter
  },
  {
    type: 'keyValueGrid',
    label: 'Key-Value Grid',
    description: 'Definition pairs',
    icon: Hash
  }
];

export const ContentTypeSelector: React.FC = () => {
  const { addBlock, getCurrentStep } = useExplainerStore();

  const currentStep = getCurrentStep();

  const handleAddBlock = (type: ContentBlock['type']) => {
    if (!currentStep) return;
    addBlock(type);
  };

  if (!currentStep) {
    return (
      <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
        <div className="text-center text-gray-500">
          <div className="text-2xl mb-2">📝</div>
          <p className="text-sm">No step selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900 mb-1">
          Content Types
        </h3>
        <p className="text-sm text-gray-600">
          Click to add content to &quot;{currentStep.title}&quot;
        </p>
      </div>

      <div className="flex-1 p-4 space-y-2 overflow-y-auto">
        {CONTENT_TYPES.map((contentType) => {
          const IconComponent = contentType.icon;

          return (
            <Button
              key={contentType.type}
              variant="outline"
              className="w-full justify-start h-auto p-3 text-left hover:bg-blue-50 hover:border-blue-200"
              onClick={() => handleAddBlock(contentType.type)}
            >
              <div className="flex items-start gap-3">
                <IconComponent className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="min-w-0">
                  <div className="font-medium text-gray-900 text-sm">
                    {contentType.label}
                  </div>
                  <div className="text-xs text-gray-600 mt-0.5">
                    {contentType.description}
                  </div>
                </div>
              </div>
            </Button>
          );
        })}
      </div>

      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-600">
          <div className="font-medium mb-1">Current Step:</div>
          <div className="flex items-center gap-2">
            <span className="truncate">{currentStep.title}</span>
            <span className="text-blue-600">
              ({currentStep.blocks.length} blocks)
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

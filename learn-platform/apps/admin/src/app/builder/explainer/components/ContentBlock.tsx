/**
 * Content Block - Individual content block wrapper with drag-and-drop and editing
 */

'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from '@learn-platform/shared-ui';
import { GripVertical, Edit2, Trash2 } from 'lucide-react';
import { useExplainerStore } from '../store/explainerStore';
import { ContentBlock as ContentBlockType } from '../types';

// Import inline editors
import { ParagraphEditor } from './InlineEditors/ParagraphEditor';
import { InfoBoxEditor } from './InlineEditors/InfoBoxEditor';
import { ListEditor } from './InlineEditors/ListEditor';
import { GridEditor } from './InlineEditors/GridEditor';
import { ComparisonEditor } from './InlineEditors/ComparisonEditor';
import { TableEditor } from './InlineEditors/TableEditor';
import { ScatterPlotEditor } from './InlineEditors/ScatterPlotEditor';
import { KeyValueEditor } from './InlineEditors/KeyValueEditor';

interface ContentBlockProps {
  block: ContentBlockType;
  stepId: string;
  index: number;
}

export const ContentBlock: React.FC<ContentBlockProps> = ({
  block,
  stepId,
  index
}) => {
  const {
    editingBlockId,
    setEditingBlock,
    updateBlock,
    deleteBlock
  } = useExplainerStore();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: block.id,
    data: {
      type: 'content-block',
      blockId: block.id,
      stepId,
      index
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const isEditing = editingBlockId === block.id;

  const handleStartEdit = () => {
    setEditingBlock(block.id);
  };

  const handleSaveEdit = (newData: any) => {
    updateBlock(block.id, newData);
    setEditingBlock(null);
  };

  const handleCancelEdit = () => {
    setEditingBlock(null);
  };

  const handleDelete = () => {
    deleteBlock(block.id);
  };

  const renderEditor = () => {
    const commonProps = {
      data: block.data,
      onSave: handleSaveEdit,
      onCancel: handleCancelEdit
    };

    switch (block.type) {
      case 'paragraph':
        return <ParagraphEditor {...commonProps} />;
      case 'infoBox':
        return <InfoBoxEditor {...commonProps} />;
      case 'bulletList':
      case 'numberedList':
        return <ListEditor {...commonProps} type={block.type} />;
      case 'grid':
        return <GridEditor {...commonProps} />;
      case 'comparison':
        return <ComparisonEditor {...commonProps} />;
      case 'table':
        return <TableEditor {...commonProps} />;
      case 'scatterPlot':
        return <ScatterPlotEditor {...commonProps} />;
      case 'keyValueGrid':
        return <KeyValueEditor {...commonProps} />;
      default:
        return <div>Unknown content type: {block.type}</div>;
    }
  };

  const renderPreview = () => {
    // Simple preview rendering - you can enhance this with actual component rendering
    switch (block.type) {
      case 'paragraph':
        return (
          <div className="prose prose-sm max-w-none">
            {Array.isArray(block.data) ? (
              block.data.map((paragraph, i) => (
                <p key={i} className="mb-2">{paragraph}</p>
              ))
            ) : (
              <p>{block.data}</p>
            )}
          </div>
        );
      case 'infoBox':
        return (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            {block.data.heading && (
              <h4 className="font-semibold text-blue-900 mb-2">
                {block.data.heading}
              </h4>
            )}
            <ul className="text-blue-800 space-y-1">
              {block.data.lines.map((line: string, i: number) => (
                <li key={i}>• {line}</li>
              ))}
            </ul>
          </div>
        );
      case 'bulletList':
        return (
          <ul className="list-disc list-inside space-y-1">
            {block.data.map((item: string, i: number) => (
              <li key={i}>{item}</li>
            ))}
          </ul>
        );
      case 'numberedList':
        return (
          <ol className="list-decimal list-inside space-y-1">
            {block.data.map((item: string, i: number) => (
              <li key={i}>{item}</li>
            ))}
          </ol>
        );
      default:
        return (
          <div className="text-gray-500 italic">
            {block.type} content (preview not implemented)
          </div>
        );
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        group relative bg-white border border-gray-200 rounded-lg
        transition-all duration-200
        ${isDragging ? 'shadow-lg opacity-50' : 'hover:shadow-md'}
        ${isEditing ? 'ring-2 ring-blue-500 border-blue-300' : ''}
      `}
    >
      {/* Drag handle and controls */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100"
          >
            <GripVertical className="w-4 h-4 text-gray-400" />
          </div>
          <span className="text-sm font-medium text-gray-700 capitalize">
            {block.type.replace(/([A-Z])/g, ' $1').trim()}
          </span>
        </div>

        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              className="w-8 h-8 p-0"
              onClick={handleStartEdit}
            >
              <Edit2 className="w-4 h-4" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="w-8 h-8 p-0 text-red-500 hover:text-red-700"
            onClick={handleDelete}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {isEditing ? renderEditor() : renderPreview()}
      </div>
    </div>
  );
};

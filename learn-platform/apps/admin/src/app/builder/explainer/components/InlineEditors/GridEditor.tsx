/**
 * Grid Editor - Inline editor for grid content
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import { Textarea } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2 } from 'lucide-react';
import { GridData } from '../../../../../lib/components/templates/types';

interface GridEditorProps {
  data: GridData[];
  onSave: (data: GridData[]) => void;
  onCancel: () => void;
}

export const GridEditor: React.FC<GridEditorProps> = ({
  data,
  onSave,
  onCancel
}) => {
  const [items, setItems] = useState<GridData[]>(data || [
    { title: '', content: '' }
  ]);

  const handleItemChange = (index: number, field: keyof GridData, value: string) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };
    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, { title: '', content: '' }]);
  };

  const removeItem = (index: number) => {
    if (items.length <= 1) return;
    const newItems = items.filter((_, i) => i !== index);
    setItems(newItems);
  };

  const handleSave = () => {
    const filteredItems = items.filter(item =>
      item.title.trim() !== '' || item.content.trim() !== ''
    );
    if (filteredItems.length === 0) {
      filteredItems.push({ title: 'Grid Item', content: 'Content' });
    }
    onSave(filteredItems);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Grid Items</h4>
        <span className="text-xs text-gray-500">
          {items.length} item{items.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="grid gap-4">
        {items.map((item, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">
                Item {index + 1}
              </span>
              {items.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-8 h-8 p-0 text-red-500 hover:text-red-700"
                  onClick={() => removeItem(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>

            <div className="space-y-2">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Title
                </label>
                <Input
                  value={item.title}
                  onChange={(e) => handleItemChange(index, 'title', e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Grid item title..."
                  autoFocus={index === 0}
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Content
                </label>
                <Textarea
                  value={item.content}
                  onChange={(e) => handleItemChange(index, 'content', e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Grid item content..."
                  className="min-h-[80px] resize-none"
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between pt-2">
        <Button
          variant="outline"
          size="sm"
          onClick={addItem}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Grid Item
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            className="flex items-center gap-2"
          >
            <Check className="w-4 h-4" />
            Save
          </Button>
        </div>
      </div>

      <div className="text-xs text-gray-500">
        Press Ctrl/Cmd + Enter to save, Escape to cancel
      </div>
    </div>
  );
};

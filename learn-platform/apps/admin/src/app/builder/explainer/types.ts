/**
 * Types for the Explainer Builder
 */

import { StepConfig } from '../../../lib/components/templates/types';

export interface ContentBlock {
  id: string;
  type: StepConfig['type'];
  data: any;
  isEditing?: boolean;
}

export interface ExplainerStep {
  id: string;
  title: string;
  icon: string; // Icon name for builder (will be converted to ReactNode for rendering)
  blocks: ContentBlock[];
}

export interface Explainer {
  id: string;
  name: string;
  description?: string;
  steps: ExplainerStep[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ExplainerBuilderState {
  // Current explainer being edited
  explainer: Explainer | null;

  // Current active step index
  currentStepIndex: number;

  // Currently editing block ID
  editingBlockId: string | null;

  // UI state
  isPreviewMode: boolean;
  isSaving: boolean;

  // Actions
  createNewExplainer: (name: string) => void;
  loadExplainer: (explainer: Explainer) => void;
  updateExplainerInfo: (updates: Partial<Pick<Explainer, 'name' | 'description'>>) => void;

  // Step management
  addStep: () => void;
  updateStep: (stepId: string, updates: Partial<Omit<ExplainerStep, 'id' | 'blocks'>>) => void;
  deleteStep: (stepId: string) => void;
  setCurrentStep: (index: number) => void;
  reorderSteps: (fromIndex: number, toIndex: number) => void;

  // Block management
  addBlock: (type: ContentBlock['type'], stepId?: string) => void;
  updateBlock: (blockId: string, data: any) => void;
  deleteBlock: (blockId: string) => void;
  reorderBlocks: (stepId: string, fromIndex: number, toIndex: number) => void;

  // Editing state
  setEditingBlock: (blockId: string | null) => void;

  // Preview and save
  togglePreview: () => void;
  saveExplainer: () => Promise<void>;
  exportToStepConfig: () => StepConfig[];

  // Utility
  getCurrentStep: () => ExplainerStep | null;
  getBlockById: (blockId: string) => { block: ContentBlock; stepId: string } | null;
}

// Default data structures for each content type
export const DEFAULT_BLOCK_DATA = {
  paragraph: ['Enter your paragraph text here...'],
  infoBox: {
    heading: 'Information',
    lines: ['Add your information here...']
  },
  bulletList: ['First item', 'Second item', 'Third item'],
  numberedList: ['First step', 'Second step', 'Third step'],
  grid: [
    { title: 'Title 1', content: 'Content 1' },
    { title: 'Title 2', content: 'Content 2' }
  ],
  comparison: [
    { label: 'Comparison 1', before: 'Before', after: 'After' }
  ],
  table: {
    headers: ['Column 1', 'Column 2'],
    rows: [['Row 1 Col 1', 'Row 1 Col 2'], ['Row 2 Col 1', 'Row 2 Col 2']]
  },
  scatterPlot: {
    data: [
      { x: 10, y: 20, label: 'Point 1' },
      { x: 30, y: 40, label: 'Point 2' }
    ],
    width: 400,
    height: 300
  },
  keyValueGrid: [
    { key: 'Key 1', value: 'Value 1' },
    { key: 'Key 2', value: 'Value 2' }
  ]
} as const;

// Available icons for steps (must match IconName from iconMapping.ts)
export const AVAILABLE_ICONS = [
  'BookOpen',
  'Lightbulb',
  'Target',
  'CheckCircle',
  'Info',
  'AlertCircle',
  'Star',
  'Zap',
  'Rocket',
  'Heart',
  'Award',
  'Flag'
] as const;

export type AvailableIcon = typeof AVAILABLE_ICONS[number];

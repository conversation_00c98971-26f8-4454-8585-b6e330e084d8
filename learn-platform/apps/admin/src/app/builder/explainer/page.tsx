/**
 * Explainer Templates Page - Template listing and management
 */

'use client';

import React from 'react';
import Link from 'next/link';

import { ProtectedRoute } from '../../../components/auth/protected-route';
import { AdminLayout } from '../../../lib/components/layout';
import { TemplateList } from './components/TemplateList';

export default function ExplainerTemplatesPage() {
  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="h-full flex flex-col">
          {/* Breadcrumb Navigation */}
          <div className="mb-4 flex items-center text-sm text-gray-600">
            <Link href="/dashboard" className="hover:text-gray-900">
              Dashboard
            </Link>
            <span className="mx-2">/</span>
            <Link href="/builder" className="hover:text-gray-900">
              Builder
            </Link>
            <span className="mx-2">/</span>
            <span className="text-gray-900 font-medium">Templates</span>
          </div>

          {/* Template List */}
          <div className="flex-1 min-h-0">
            <TemplateList />
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

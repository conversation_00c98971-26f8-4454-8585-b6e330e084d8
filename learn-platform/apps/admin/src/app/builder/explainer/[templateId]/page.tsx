/**
 * Individual Template Editor Page - Edit specific explainer template
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@learn-platform/shared-ui';
import { <PERSON>Lef<PERSON>, Loader2 } from 'lucide-react';

import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { AdminLayout } from '../../../../lib/components/layout';
import { useExplainerStore } from '../store/explainerStore';
import { ExplainerBuilder } from '../components/ExplainerBuilder';
import { trpc } from '../../../../lib/trpc';

export default function TemplateEditorPage() {
  const params = useParams();
  const router = useRouter();
  const templateId = params.templateId as string;
  
  const { loadExplainer, explainer } = useExplainerStore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch template data
  const { data: templateData, isLoading: isFetching, error: fetchError } = trpc.templates.getById.useQuery(
    { id: templateId },
    { 
      enabled: !!templateId,
      retry: false,
    }
  );

  useEffect(() => {
    if (templateData?.success && templateData.template) {
      // Convert database template to explainer format
      const template = templateData.template;
      const explainerData = {
        id: template.id,
        name: template.name,
        description: template.description || '',
        steps: template.steps,
        createdAt: new Date(template.createdAt),
        updatedAt: new Date(template.updatedAt),
      };
      
      loadExplainer(explainerData);
      setIsLoading(false);
    } else if (fetchError) {
      setError(fetchError.message || 'Failed to load template');
      setIsLoading(false);
    } else if (!isFetching) {
      setIsLoading(false);
    }
  }, [templateData, fetchError, isFetching, loadExplainer]);

  // Handle loading state
  if (isLoading || isFetching) {
    return (
      <ProtectedRoute redirectTo="/login">
        <AdminLayout>
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Loading Template
              </h3>
              <p className="text-gray-600">
                Please wait while we load your template...
              </p>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  // Handle error state
  if (error) {
    return (
      <ProtectedRoute redirectTo="/login">
        <AdminLayout>
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="text-4xl mb-4">❌</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Template Not Found
              </h3>
              <p className="text-gray-600 mb-6">
                {error}
              </p>
              <Link href="/builder/explainer">
                <Button variant="outline">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Templates
                </Button>
              </Link>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="h-full flex flex-col">
          {/* Breadcrumb Navigation */}
          <div className="mb-4 flex items-center text-sm text-gray-600">
            <Link href="/dashboard" className="hover:text-gray-900">
              Dashboard
            </Link>
            <span className="mx-2">/</span>
            <Link href="/builder" className="hover:text-gray-900">
              Builder
            </Link>
            <span className="mx-2">/</span>
            <Link href="/builder/explainer" className="hover:text-gray-900">
              Templates
            </Link>
            <span className="mx-2">/</span>
            <span className="text-gray-900 font-medium">
              {explainer?.name || 'Template'}
            </span>
          </div>

          {/* Back Button */}
          <div className="mb-4">
            <Link href="/builder/explainer">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Templates
              </Button>
            </Link>
          </div>

          {/* Template Editor */}
          <div className="flex-1 min-h-0">
            <ExplainerBuilder />
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

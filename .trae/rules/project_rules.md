1. for running web project on the local use `bunx nx run web:dev` from `/Users/<USER>/Documents/personal-project/learning-monorepo/learn-platform`
2. for running admin project on the local use `bunx nx run admin:dev` from `/Users/<USER>/Documents/personal-project/learning-monorepo/learn-platform`
3. for running api project on the local use `bunx nx run api:dev` from `/Users/<USER>/Documents/personal-project/learning-monorepo/learn-platform`
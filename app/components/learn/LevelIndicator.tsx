'use client';

import { cn } from '@learn-platform/shared-ui';

interface LevelIndicatorProps {
  level: 'beginner' | 'intermediate' | 'advanced';
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function LevelIndicator({ level, className, size = 'md' }: LevelIndicatorProps) {
  // Determine number of bars based on level
  const getBarCount = (level: string) => {
    switch (level) {
      case 'beginner':
        return 1;
      case 'intermediate':
        return 2;
      case 'advanced':
        return 3;
      default:
        return 1;
    }
  };

  // Get color classes based on level
  const getColorClasses = (level: string) => {
    switch (level) {
      case 'beginner':
        return {
          active: 'bg-green-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
      case 'intermediate':
        return {
          active: 'bg-yellow-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
      case 'advanced':
        return {
          active: 'bg-red-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
      default:
        return {
          active: 'bg-green-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
    }
  };

  // Get size classes
  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          container: 'gap-0.5',
          bar: 'w-1 h-3'
        };
      case 'md':
        return {
          container: 'gap-1',
          bar: 'w-1.5 h-4'
        };
      case 'lg':
        return {
          container: 'gap-1.5',
          bar: 'w-2 h-5'
        };
      default:
        return {
          container: 'gap-1',
          bar: 'w-1.5 h-4'
        };
    }
  };

  const barCount = getBarCount(level);
  const colorClasses = getColorClasses(level);
  const sizeClasses = getSizeClasses(size);
  const totalBars = 3; // Always show 3 bars total

  return (
    <div className={cn(
      'flex items-end',
      sizeClasses.container,
      className
    )}>
      {Array.from({ length: totalBars }, (_, index) => (
        <div
          key={index}
          className={cn(
            'rounded-sm transition-colors duration-200',
            sizeClasses.bar,
            index < barCount ? colorClasses.active : colorClasses.inactive
          )}
        />
      ))}
    </div>
  );
}
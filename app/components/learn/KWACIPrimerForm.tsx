'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@learn-platform/shared-ui';
import { Brain, Loader2, Lightbulb, Target, BookOpen, Sparkles, Info } from 'lucide-react';

// Validation schema for KWACI primer
const kwaciPrimerInputSchema = z.object({
  topic: z.string().min(3, 'Topic must be at least 3 characters').max(200, 'Topic must be less than 200 characters'),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  focusAreas: z.string().max(500, 'Focus areas must be less than 500 characters').optional(),
});

type KWACIPrimerInputData = z.infer<typeof kwaciPrimerInputSchema>;

interface KWACIPrimerFormProps {
  onSubmit?: (data: KWACIPrimerInputData) => void;
  isLoading?: boolean;
  initialValues?: Partial<KWACIPrimerInputData>;
  onCancel?: () => void;
}

export function KWACIPrimerForm({ onSubmit, isLoading = false, initialValues }: KWACIPrimerFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    reset,
  } = useForm<KWACIPrimerInputData>({
    resolver: zodResolver(kwaciPrimerInputSchema),
    defaultValues: {
      topic: initialValues?.topic || '',
      learningLevel: initialValues?.learningLevel || 'beginner',
      focusAreas: initialValues?.focusAreas || '',
    },
    mode: 'onChange',
  });

  const handleFormSubmit = (data: KWACIPrimerInputData) => {
    onSubmit?.(data);
  };

  const handleReset = () => {
    reset();
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Sparkles className="h-6 w-6 text-purple-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Create KWACI Primer</h2>
        </div>
        <p className="text-gray-600 dark:text-gray-300">
          Generate a comprehensive 9-section KWACI (Knowledge With Analogy, Components, and Implementation) primer 
          that provides structured, engaging educational content.
        </p>
      </div>

      {/* KWACI Structure Info */}
      <div className="mb-6 p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-purple-800 dark:text-purple-200 mb-2">
              KWACI Primer Structure (9 Fixed Sections)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-purple-700 dark:text-purple-300">
              <div>🧠 TL;DR Summary</div>
              <div>🎭 Analogy Section</div>
              <div>🧱 Core Components</div>
              <div>⚙️ How It Works</div>
              <div>📊 Trade-Offs/Comparisons</div>
              <div>💻 Code/Syntax Examples</div>
              <div>🚧 Common Pitfalls</div>
              <div>🧪 Knowledge Check Questions</div>
              <div>📚 Want to go deeper</div>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Topic Input */}
        <div>
          <label htmlFor="topic" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Target className="h-4 w-4 inline mr-1" />
            What topic do you want to learn about? *
          </label>
          <input
            {...register('topic')}
            type="text"
            id="topic"
            placeholder="e.g., Machine Learning Fundamentals, React Hooks, Database Indexing..."
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
              errors.topic ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={isLoading}
          />
          {errors.topic && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.topic.message}</p>
          )}
        </div>

        {/* Learning Level */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <BookOpen className="h-4 w-4 inline mr-1" />
            Learning Level *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {[
              { value: 'beginner', label: 'Beginner', description: 'New to this topic' },
              { value: 'intermediate', label: 'Intermediate', description: 'Some basic knowledge' },
              { value: 'advanced', label: 'Advanced', description: 'Solid foundation' },
            ].map((level) => (
              <label
                key={level.value}
                className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                  watch('learningLevel') === level.value
                    ? 'border-purple-600 bg-purple-50 dark:bg-purple-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                }`}
              >
                <input
                  {...register('learningLevel')}
                  type="radio"
                  value={level.value}
                  className="sr-only"
                  disabled={isLoading}
                />
                <div className="flex w-full items-center justify-between">
                  <div className="flex items-center">
                    <div className="text-sm">
                      <div className={`font-medium ${
                        watch('learningLevel') === level.value
                          ? 'text-purple-900 dark:text-purple-100'
                          : 'text-gray-900 dark:text-white'
                      }`}>
                        {level.label}
                      </div>
                      <div className={`${
                        watch('learningLevel') === level.value
                          ? 'text-purple-700 dark:text-purple-300'
                          : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {level.description}
                      </div>
                    </div>
                  </div>
                  {watch('learningLevel') === level.value && (
                    <div className="flex-shrink-0 text-purple-600 dark:text-purple-400">
                      <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  )}
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Focus Areas */}
        <div>
          <label htmlFor="focusAreas" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Lightbulb className="h-4 w-4 inline mr-1" />
            Specific Focus Areas (Optional)
          </label>
          <textarea
            {...register('focusAreas')}
            id="focusAreas"
            rows={3}
            placeholder="e.g., practical applications, best practices, common mistakes to avoid..."
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none ${
              errors.focusAreas ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={isLoading}
          />
          {errors.focusAreas && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.focusAreas.message}</p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {watch('focusAreas')?.length || 0}/500 characters
          </p>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleReset}
            disabled={isLoading}
          >
            Reset
          </Button>
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating KWACI Primer...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate KWACI Primer
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
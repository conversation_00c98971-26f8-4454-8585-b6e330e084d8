'use client';

import React from 'react';
import { Star, MessageSquare, ThumbsUp, RefreshCw } from 'lucide-react';

interface FeedbackSummaryProps {
  contentId: string;
  stats?: {
    totalFeedback: number;
    averageRating: number;
    helpfulCount: number;
    regenerationRequests: number;
    ratingDistribution: {
      1: number;
      2: number;
      3: number;
      4: number;
      5: number;
    };
  };
  userFeedback?: {
    rating: number;
    isHelpful?: boolean;
    requestRegeneration?: boolean;
  };
  showDetailed?: boolean;
}

export function FeedbackSummary({ 
  contentId, 
  stats, 
  userFeedback, 
  showDetailed = false 
}: FeedbackSummaryProps) {
  if (!stats && !userFeedback) {
    return null;
  }

  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const starSize = size === 'sm' ? 'h-3 w-3' : 'h-4 w-4';
    
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${starSize} ${
              star <= rating
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (showDetailed && stats) {
    return (
      <div className="bg-white border rounded-lg p-4 space-y-4">
        <h4 className="font-medium text-gray-900 flex items-center space-x-2">
          <MessageSquare className="h-4 w-4" />
          <span>Feedback Summary</span>
        </h4>

        {/* Overall Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.totalFeedback}</div>
            <div className="text-xs text-gray-500">Total Reviews</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <span className="text-2xl font-bold text-yellow-600">{stats.averageRating}</span>
              <Star className="h-5 w-5 text-yellow-400 fill-current" />
            </div>
            <div className="text-xs text-gray-500">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.helpfulCount}</div>
            <div className="text-xs text-gray-500">Found Helpful</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{stats.regenerationRequests}</div>
            <div className="text-xs text-gray-500">Regen Requests</div>
          </div>
        </div>

        {/* Rating Distribution */}
        <div className="space-y-2">
          <h5 className="text-sm font-medium text-gray-700">Rating Distribution</h5>
          {[5, 4, 3, 2, 1].map((rating) => {
            const count = stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution];
            const percentage = stats.totalFeedback > 0 ? (count / stats.totalFeedback) * 100 : 0;
            
            return (
              <div key={rating} className="flex items-center space-x-2 text-sm">
                <span className="w-8">{rating}</span>
                <Star className="h-3 w-3 text-yellow-400 fill-current" />
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-400 h-2 rounded-full"
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <span className="w-8 text-gray-500">{count}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // Simple summary for cards
  return (
    <div className="flex items-center space-x-4 text-sm text-gray-600">
      {userFeedback && (
        <div className="flex items-center space-x-1">
          <span className="text-xs">Your rating:</span>
          {renderStars(userFeedback.rating)}
        </div>
      )}
      
      {stats && stats.totalFeedback > 0 && (
        <>
          <div className="flex items-center space-x-1">
            <span>{stats.averageRating}</span>
            <Star className="h-3 w-3 text-yellow-400 fill-current" />
            <span className="text-xs">({stats.totalFeedback})</span>
          </div>
          
          {stats.helpfulCount > 0 && (
            <div className="flex items-center space-x-1">
              <ThumbsUp className="h-3 w-3 text-green-500" />
              <span className="text-xs">{stats.helpfulCount} helpful</span>
            </div>
          )}
          
          {stats.regenerationRequests > 0 && (
            <div className="flex items-center space-x-1">
              <RefreshCw className="h-3 w-3 text-orange-500" />
              <span className="text-xs">{stats.regenerationRequests} regen</span>
            </div>
          )}
        </>
      )}
    </div>
  );
}
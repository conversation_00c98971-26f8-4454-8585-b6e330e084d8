'use client';

import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { Button } from '@learn-platform/shared-ui';
import {
  CheckCircle,
  Circle,
  Clock,
  Trophy,
  Bookmark,
  BookmarkCheck,
  StickyNote
} from 'lucide-react';
import { api } from '../../trpc';

interface ProgressTrackerProps {
  contentId: string;
  totalSteps: number;
  currentStep: number;
  onStepChange?: (step: number) => void;
  onProgressUpdate?: (progress: any) => void;
  compact?: boolean;
  layout?: 'sidebar' | 'card';
}

export function ProgressTracker({
  contentId,
  totalSteps,
  currentStep,
  onStepChange,
  onProgressUpdate,
  compact = false,
  layout = 'sidebar'
}: ProgressTrackerProps) {
  const [timeSpent, setTimeSpent] = useState(0);
  const [showNoteDialog, setShowNoteDialog] = useState(false);
  const [noteContent, setNoteContent] = useState('');
  const [editingNoteIndex, setEditingNoteIndex] = useState<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastUserStep, setLastUserStep] = useState<number | null>(null);

  // Refs for debouncing and preventing duplicate calls
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<{
    contentId: string;
    currentStepIndex: number;
    timeSpent: number;
    completedSteps: number[];
  } | null>(null);

  // Get tRPC utils for cache invalidation
  const utils = api.useUtils();

  // Fetch current progress
  const { data: progressData } = api.learningProgress.getProgress.useQuery(
    { contentId },
    {
      enabled: !!contentId,
      staleTime: 30 * 1000, // 30 seconds - reduce frequent refetches during active learning
    }
  );

  // Progress update mutation
  const updateProgressMutation = api.learningProgress.updateProgress.useMutation({
    onSuccess: (data) => {
      // Invalidate queries to update cache - React Query will handle refetching automatically
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
      if (onProgressUpdate) {
        onProgressUpdate(data.progress);
      }
    },
  });

  // Bookmark mutations
  const addBookmarkMutation = api.learningProgress.addBookmark.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
    },
  });

  const removeBookmarkMutation = api.learningProgress.removeBookmark.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
    },
  });

  // Note mutations
  const addNoteMutation = api.learningProgress.addNote.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
      setShowNoteDialog(false);
      setNoteContent('');
      setEditingNoteIndex(null);
    },
  });

  const updateNoteMutation = api.learningProgress.updateNote.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
      setShowNoteDialog(false);
      setNoteContent('');
      setEditingNoteIndex(null);
    },
  });

  const deleteNoteMutation = api.learningProgress.deleteNote.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
    },
  });

  const progress = progressData?.progress;

  // Memoize arrays to prevent useEffect dependency issues
  const completedSteps = useMemo(() => progress?.completedSteps || [], [progress?.completedSteps]);
  const bookmarks = useMemo(() => progress?.bookmarks || [], [progress?.bookmarks]);
  const notes = useMemo(() => progress?.notes || [], [progress?.notes]);

  // Initialize currentStep from progress data on first load
  useEffect(() => {
    if (progress && !isInitialized) {
      // If we have progress data and haven't initialized yet,
      // sync the parent component's currentStep with the saved progress
      if (onStepChange && progress.currentStepIndex !== currentStep) {
        onStepChange(progress.currentStepIndex);
      }
      setIsInitialized(true);
      // Set initial lastUserStep to current progress to enable future updates
      setLastUserStep(progress.currentStepIndex);
    } else if (!progress && !isInitialized) {
      // If there's no progress data (new learning content), initialize anyway
      setIsInitialized(true);
      // Set lastUserStep to current step to enable progress tracking from the start
      setLastUserStep(currentStep);
    }
  }, [progress, isInitialized, currentStep, onStepChange]);

  // Handle external step changes (from MultiStepExplain Next/Prev buttons)
  // This effect is now consolidated with the progress update effect below

  // Track time spent
  useEffect(() => {
    const startTime = new Date();
    const interval = setInterval(() => {
      const now = new Date();
      const sessionTime = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setTimeSpent(sessionTime);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Debounced progress update function to prevent rapid API calls
  const debouncedUpdateProgress = useCallback(() => {
    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set a new timeout for the update
    debounceTimeoutRef.current = setTimeout(() => {
      if (
        isInitialized &&
        lastUserStep !== null && // This ensures we've had at least one interaction
        (
          // Either we have progress and current step is different
          (progress && currentStep !== progress.currentStepIndex) ||
          // Or we don't have progress yet (first time tracking)
          !progress
        )
      ) {
        const newCompletedSteps = [...completedSteps];

        // Mark steps as completed when moving forward
        const previousStep = progress?.currentStepIndex || 0;
        if (currentStep > previousStep) {
          for (let i = previousStep; i < currentStep; i++) {
            if (!newCompletedSteps.includes(i)) {
              newCompletedSteps.push(i);
            }
          }
        }

        // If we're on the final step, mark it as completed
        if (currentStep === totalSteps - 1 && !newCompletedSteps.includes(currentStep)) {
          newCompletedSteps.push(currentStep);
        }

        // Check if this update is different from the last one to prevent duplicates
        const updateData = {
          contentId,
          currentStepIndex: currentStep,
          timeSpent,
          completedSteps: newCompletedSteps,
        };

        const lastUpdate = lastUpdateRef.current;
        const isDifferent = !lastUpdate ||
          lastUpdate.contentId !== updateData.contentId ||
          lastUpdate.currentStepIndex !== updateData.currentStepIndex ||
          lastUpdate.timeSpent !== updateData.timeSpent ||
          JSON.stringify(lastUpdate.completedSteps) !== JSON.stringify(updateData.completedSteps);

        if (isDifferent && !updateProgressMutation.isPending) {
          lastUpdateRef.current = updateData;
          updateProgressMutation.mutate(updateData);
        }
      }
    }, 500); // 500ms debounce delay
  }, [isInitialized, progress, currentStep, lastUserStep, completedSteps, timeSpent, totalSteps, contentId, updateProgressMutation]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Update progress when step changes (only after initialization)
  // Consolidated effect to prevent duplicate API calls
  useEffect(() => {
    if (isInitialized) {
      // Update lastUserStep first to track the change
      if (currentStep !== lastUserStep) {
        setLastUserStep(currentStep);
        // Only call debounced update if this is actually a new step
        debouncedUpdateProgress();
      }
    }
  }, [currentStep, isInitialized, lastUserStep, debouncedUpdateProgress]);

  const handleStepClick = (stepIndex: number) => {
    // Let the parent component handle the step change
    // The useEffect will handle updating lastUserStep and progress
    if (onStepChange) {
      onStepChange(stepIndex);
    }
  };

  const handleToggleBookmark = () => {
    if (isStepBookmarked) {
      // Remove bookmark
      removeBookmarkMutation.mutate({
        contentId,
        stepIndex: currentStep,
      });
    } else {
      // Add bookmark
      addBookmarkMutation.mutate({
        contentId,
        stepIndex: currentStep,
        note: `Bookmarked step ${currentStep + 1}`,
      });
    }
  };

  const handleAddNote = () => {
    if (noteContent.trim()) {
      if (editingNoteIndex !== null) {
        // Update existing note
        updateNoteMutation.mutate({
          contentId,
          stepIndex: currentStep,
          noteIndex: editingNoteIndex,
          content: noteContent.trim(),
        });
      } else {
        // Add new note
        addNoteMutation.mutate({
          contentId,
          stepIndex: currentStep,
          content: noteContent.trim(),
        });
      }
    }
  };

  const handleEditNote = (noteIndex: number, content: string) => {
    setEditingNoteIndex(noteIndex);
    setNoteContent(content);
    setShowNoteDialog(true);
  };

  const handleDeleteNote = (noteIndex: number) => {
    deleteNoteMutation.mutate({
      contentId,
      stepIndex: currentStep,
      noteIndex,
    });
  };

  const isStepBookmarked = bookmarks.some((b: any) => b.stepIndex === currentStep);
  const stepNotes = notes.filter((n: any) => n.stepIndex === currentStep);
  const completionPercentage = progress?.completionPercentage || 0;

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Compact version for sidebar
  if (compact) {
    return (
      <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-3 space-y-3 h-full flex flex-col">
        {/* Compact Header */}
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Progress</h4>
          <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
            <Clock className="h-3 w-3" />
            <span>{formatTime(timeSpent)}</span>
          </div>
        </div>

        {/* Compact Progress Bar */}
        <div className="space-y-1">
          <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
            <span>Step {currentStep + 1}/{totalSteps}</span>
            <span className="font-medium text-blue-600 dark:text-blue-400">{completionPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
            <div
              className="bg-blue-600 dark:bg-blue-500 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Compact Step Navigation */}
        <div className="flex flex-wrap gap-1 flex-shrink-0">
          {Array.from({ length: Math.min(totalSteps, 8) }, (_, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep;

            return (
              <button
                key={index}
                onClick={() => handleStepClick(index)}
                className={`w-5 h-5 rounded text-xs font-medium transition-all ${
                  isCurrent
                    ? 'bg-blue-500 text-white'
                    : isCompleted
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500'
                }`}
              >
                {index + 1}
              </button>
            );
          })}
          {totalSteps > 8 && (
            <span className="text-xs text-gray-500 dark:text-gray-400 self-center">+{totalSteps - 8}</span>
          )}
        </div>

        {/* Complete Indicator */}
        {progress?.isCompleted && (
          <div className="flex items-center justify-center space-x-1 text-green-600 dark:text-green-400 text-xs font-medium bg-green-50 dark:bg-green-900/20 rounded p-2 flex-shrink-0">
            <Trophy className="h-3 w-3" />
            <span>Complete!</span>
          </div>
        )}
      </div>
    );
  }

  // Card version for two-column layout
  if (layout === 'card') {
    return (
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg border dark:border-gray-700 p-4">
        {/* Card Header */}
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-base font-semibold text-gray-900 dark:text-white">Learning Progress</h3>
          <div className="flex items-center space-x-1 text-xs text-gray-600 dark:text-gray-300">
            <Clock className="h-3 w-3" />
            <span>{formatTime(timeSpent)}</span>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="space-y-3">
          {/* Current Step Info */}
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600 dark:text-gray-300">
              Step {currentStep + 1} of {totalSteps}
            </span>
            <span className="text-base font-bold text-blue-600 dark:text-blue-400">
              {completionPercentage}%
            </span>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>

          {/* Step Navigation - Horizontal Slider */}
          <div className="space-y-1">
            <div className="text-xs font-medium text-gray-700 dark:text-gray-300">Quick Navigation</div>
            <div className="flex space-x-1 overflow-x-auto pb-1">
              {Array.from({ length: totalSteps }, (_, index) => {
                const isCompleted = completedSteps.includes(index);
                const isCurrent = index === currentStep;

                return (
                  <button
                    key={index}
                    onClick={() => handleStepClick(index)}
                    className={`flex-shrink-0 w-6 h-6 rounded-full text-xs font-medium transition-all ${
                      isCurrent
                        ? 'bg-blue-500 text-white shadow-lg'
                        : isCompleted
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500'
                    }`}
                    title={`Go to step ${index + 1}`}
                  >
                    {index + 1}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Completion Status */}
          {progress?.isCompleted && (
            <div className="flex items-center justify-center space-x-1 text-green-600 dark:text-green-400 text-xs font-medium bg-green-50 dark:bg-green-900/20 rounded-lg p-2">
              <Trophy className="h-3 w-3" />
              <span>Complete!</span>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Full version for main layout
  return (
    <div className="bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg p-4 space-y-4">
      {/* Progress Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900 dark:text-white">Learning Progress</h3>
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
          <Clock className="h-4 w-4" />
          <span>{formatTime(timeSpent)}</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-300">
            Step {currentStep + 1} of {totalSteps}
          </span>
          <span className="font-medium text-blue-600 dark:text-blue-400">
            {completionPercentage}% Complete
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Step Navigation */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Steps</h4>
        <div className="grid grid-cols-5 gap-2">
          {Array.from({ length: totalSteps }, (_, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep;
            const isBookmarked = bookmarks.some((b: any) => b.stepIndex === index);
            const hasNotes = notes.some((n: any) => n.stepIndex === index);

            return (
              <button
                key={index}
                onClick={() => handleStepClick(index)}
                className={`relative p-2 rounded-lg border-2 transition-all ${
                  isCurrent
                    ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : isCompleted
                    ? 'border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-center">
                  {isCompleted ? (
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                  ) : (
                    <Circle className={`h-5 w-5 ${isCurrent ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500'}`} />
                  )}
                </div>
                <div className="text-xs mt-1 font-medium text-gray-900 dark:text-gray-100">
                  {index + 1}
                </div>

                {/* Indicators */}
                <div className="absolute -top-1 -right-1 flex space-x-1">
                  {isBookmarked && (
                    <BookmarkCheck className="h-3 w-3 text-yellow-500 dark:text-yellow-400" />
                  )}
                  {hasNotes && (
                    <StickyNote className="h-3 w-3 text-purple-500 dark:text-purple-400" />
                  )}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Complete Indicator - Full Width Container */}
      {progress?.isCompleted && (
        <div className="w-full mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center justify-center space-x-2 text-green-600 dark:text-green-400 font-medium">
            <Trophy className="h-5 w-5" />
            <span>Completed!</span>
          </div>
        </div>
      )}

      {/* Current Step Actions */}
      <div className="flex items-center justify-between pt-2 border-t dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleBookmark}
            className="flex items-center space-x-1"
          >
            {isStepBookmarked ? (
              <BookmarkCheck className="h-4 w-4 text-yellow-500" />
            ) : (
              <Bookmark className="h-4 w-4" />
            )}
            <span>{isStepBookmarked ? 'Remove Bookmark' : 'Bookmark'}</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowNoteDialog(true)}
            className="flex items-center space-x-1"
          >
            <StickyNote className="h-4 w-4" />
            <span>Note</span>
            {stepNotes.length > 0 && (
              <span className="bg-purple-100 text-purple-800 text-xs px-1.5 py-0.5 rounded-full">
                {stepNotes.length}
              </span>
            )}
          </Button>
        </div>
      </div>

      {/* Note Dialog */}
      {showNoteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              {editingNoteIndex !== null ? 'Edit Note' : 'Add Note'} for Step {currentStep + 1}
            </h3>
            <textarea
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              placeholder="Enter your note..."
              className="w-full h-32 p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
            <div className="flex justify-end space-x-2 mt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowNoteDialog(false);
                  setNoteContent('');
                  setEditingNoteIndex(null);
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddNote}
                disabled={!noteContent.trim()}
              >
                {editingNoteIndex !== null ? 'Update Note' : 'Add Note'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Existing Notes for Current Step */}
      {stepNotes.length > 0 && (
        <div className="space-y-2 pt-2 border-t dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Notes for this step:</h4>
          {stepNotes.map((note: any, index: number) => (
            <div key={index} className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded text-sm">
              <p className="text-gray-800 dark:text-gray-200 mb-2">{note.content}</p>
              <div className="flex justify-between items-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {new Date(note.timestamp).toLocaleString()}
                </p>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEditNote(index, note.content)}
                    className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDeleteNote(index)}
                    className="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
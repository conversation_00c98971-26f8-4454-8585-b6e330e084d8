import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { KWACIPrimerForm } from '../KWACIPrimerForm';

describe('KWACIPrimerForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the form with all required fields', () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    // Check for form title and description
    expect(screen.getByText('Create KWACI Primer')).toBeInTheDocument();
    expect(screen.getByText(/Generate a comprehensive 9-section KWACI/)).toBeInTheDocument();

    // Check for KWACI structure info
    expect(screen.getByText('KWACI Primer Structure (9 Fixed Sections)')).toBeInTheDocument();
    expect(screen.getByText('🧠 TL;DR Summary')).toBeInTheDocument();
    expect(screen.getByText('🎭 Analogy Section')).toBeInTheDocument();
    expect(screen.getByText('📚 Want to go deeper')).toBeInTheDocument();

    // Check for form fields
    expect(screen.getByLabelText(/What topic do you want to learn about/)).toBeInTheDocument();
    expect(screen.getByText('Learning Level *')).toBeInTheDocument();
    expect(screen.getByLabelText(/Specific Focus Areas/)).toBeInTheDocument();

    // Check for learning level options
    expect(screen.getByText('Beginner')).toBeInTheDocument();
    expect(screen.getByText('Intermediate')).toBeInTheDocument();
    expect(screen.getByText('Advanced')).toBeInTheDocument();

    // Check for submit button
    expect(screen.getByRole('button', { name: /Generate KWACI Primer/ })).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    const submitButton = screen.getByRole('button', { name: /Generate KWACI Primer/ });
    
    // Submit button should be disabled initially (no topic entered)
    expect(submitButton).toBeDisabled();

    // Enter a topic that's too short
    const topicInput = screen.getByLabelText(/What topic do you want to learn about/);
    fireEvent.change(topicInput, { target: { value: 'ML' } });

    await waitFor(() => {
      expect(screen.getByText('Topic must be at least 3 characters')).toBeInTheDocument();
    });
  });

  it('enables submit button when form is valid', async () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    const topicInput = screen.getByLabelText(/What topic do you want to learn about/);
    const submitButton = screen.getByRole('button', { name: /Generate KWACI Primer/ });

    // Enter a valid topic
    fireEvent.change(topicInput, { target: { value: 'Machine Learning Fundamentals' } });

    await waitFor(() => {
      expect(submitButton).toBeEnabled();
    });
  });

  it('submits form with correct data', async () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    const topicInput = screen.getByLabelText(/What topic do you want to learn about/);
    const focusAreasInput = screen.getByLabelText(/Specific Focus Areas/);
    const intermediateRadio = screen.getByLabelText(/Intermediate/);
    const submitButton = screen.getByRole('button', { name: /Generate KWACI Primer/ });

    // Fill out the form
    fireEvent.change(topicInput, { target: { value: 'React Hooks' } });
    fireEvent.change(focusAreasInput, { target: { value: 'useState and useEffect examples' } });
    fireEvent.click(intermediateRadio);

    await waitFor(() => {
      expect(submitButton).toBeEnabled();
    });

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        topic: 'React Hooks',
        learningLevel: 'intermediate',
        focusAreas: 'useState and useEffect examples',
      });
    });
  });

  it('shows loading state when isLoading is true', () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} isLoading={true} />);

    const submitButton = screen.getByRole('button', { name: /Generating KWACI Primer/ });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText('Generating KWACI Primer...')).toBeInTheDocument();

    // Form fields should be disabled
    const topicInput = screen.getByLabelText(/What topic do you want to learn about/);
    expect(topicInput).toBeDisabled();
  });

  it('populates form with initial values', () => {
    const initialValues = {
      topic: 'JavaScript Fundamentals',
      learningLevel: 'advanced' as const,
      focusAreas: 'ES6+ features and async programming',
    };

    render(<KWACIPrimerForm onSubmit={mockOnSubmit} initialValues={initialValues} />);

    const topicInput = screen.getByLabelText(/What topic do you want to learn about/) as HTMLInputElement;
    const focusAreasInput = screen.getByLabelText(/Specific Focus Areas/) as HTMLTextAreaElement;
    const advancedRadio = screen.getByLabelText(/Advanced/) as HTMLInputElement;

    expect(topicInput.value).toBe('JavaScript Fundamentals');
    expect(focusAreasInput.value).toBe('ES6+ features and async programming');
    expect(advancedRadio.checked).toBe(true);
  });

  it('resets form when reset button is clicked', async () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    const topicInput = screen.getByLabelText(/What topic do you want to learn about/) as HTMLInputElement;
    const focusAreasInput = screen.getByLabelText(/Specific Focus Areas/) as HTMLTextAreaElement;
    const resetButton = screen.getByRole('button', { name: /Reset/ });

    // Fill out the form
    fireEvent.change(topicInput, { target: { value: 'Test Topic' } });
    fireEvent.change(focusAreasInput, { target: { value: 'Test focus areas' } });

    expect(topicInput.value).toBe('Test Topic');
    expect(focusAreasInput.value).toBe('Test focus areas');

    // Reset the form
    fireEvent.click(resetButton);

    await waitFor(() => {
      expect(topicInput.value).toBe('');
      expect(focusAreasInput.value).toBe('');
    });
  });

  it('validates focus areas character limit', async () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    const focusAreasInput = screen.getByLabelText(/Specific Focus Areas/);
    const longText = 'a'.repeat(501); // Exceeds 500 character limit

    fireEvent.change(focusAreasInput, { target: { value: longText } });

    await waitFor(() => {
      expect(screen.getByText('Focus areas must be less than 500 characters')).toBeInTheDocument();
    });
  });

  it('shows character count for focus areas', () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    const focusAreasInput = screen.getByLabelText(/Specific Focus Areas/);
    
    // Initially should show 0/500
    expect(screen.getByText('0/500 characters')).toBeInTheDocument();

    // Type some text
    fireEvent.change(focusAreasInput, { target: { value: 'Test focus' } });
    expect(screen.getByText('10/500 characters')).toBeInTheDocument();
  });

  it('displays all 9 KWACI sections in the info box', () => {
    render(<KWACIPrimerForm onSubmit={mockOnSubmit} />);

    const expectedSections = [
      '🧠 TL;DR Summary',
      '🎭 Analogy Section',
      '🧱 Core Components',
      '⚙️ How It Works',
      '📊 Trade-Offs/Comparisons',
      '💻 Code/Syntax Examples',
      '🚧 Common Pitfalls',
      '🧪 Knowledge Check Questions',
      '📚 Want to go deeper',
    ];

    expectedSections.forEach(section => {
      expect(screen.getByText(section)).toBeInTheDocument();
    });
  });
});
/**
 * Tests for ChatInterface optimistic UI functionality
 */

import { describe, it, expect } from '@jest/globals';

// Mock data to test date handling
const mockServerMessage = {
  id: 'server-msg-1',
  content: 'Hello from server',
  senderRole: 'assistant' as const,
  createdAt: '2024-01-01T10:00:00.000Z', // String date from tRPC
  metadata: {
    sources: [],
  },
};

const mockOptimisticMessage = {
  id: 'optimistic-msg-1',
  content: 'Hello from user',
  senderRole: 'user' as const,
  createdAt: new Date('2024-01-01T10:01:00.000Z'), // Date object
  status: 'optimistic-user' as const,
  tempId: 'temp-1',
};

describe('ChatInterface Date Handling', () => {
  it('should handle mixed date types in message sorting', () => {
    // Simulate the date conversion logic from the component
    const serverMessage = {
      ...mockServerMessage,
      status: 'server' as const,
      createdAt: typeof mockServerMessage.createdAt === 'string' 
        ? new Date(mockServerMessage.createdAt) 
        : mockServerMessage.createdAt,
    };

    const messages = [serverMessage, mockOptimisticMessage];

    // Test sorting logic
    const sorted = messages.sort((a, b) => {
      const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() : new Date(a.createdAt).getTime();
      const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() : new Date(b.createdAt).getTime();
      return aTime - bTime;
    });

    expect(sorted[0].id).toBe('server-msg-1'); // Earlier timestamp
    expect(sorted[1].id).toBe('optimistic-msg-1'); // Later timestamp
    expect(sorted[0].createdAt instanceof Date).toBe(true);
    expect(sorted[1].createdAt instanceof Date).toBe(true);
  });

  it('should convert string dates to Date objects', () => {
    const stringDate = '2024-01-01T10:00:00.000Z';
    const convertedDate = typeof stringDate === 'string' ? new Date(stringDate) : stringDate;
    
    expect(convertedDate instanceof Date).toBe(true);
    expect(convertedDate.getTime()).toBe(new Date(stringDate).getTime());
  });

  it('should handle invalid date strings gracefully', () => {
    const invalidDate = 'invalid-date';
    const convertedDate = new Date(invalidDate);
    
    expect(convertedDate instanceof Date).toBe(true);
    expect(isNaN(convertedDate.getTime())).toBe(true);
  });
});
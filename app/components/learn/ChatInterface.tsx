'use client';

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { Button, Input, Card, CardContent, CardHeader, CardTitle } from '@learn-platform/shared-ui';
import { Send, MessageSquare, Bot, User, Loader2, AlertCircle, RotateCcw } from 'lucide-react';
import { api } from '../../trpc';
import { cn } from '@learn-platform/shared-ui';

interface ChatInterfaceProps {
  learningContentId: string;
  className?: string;
}

interface Message {
  id: string;
  content: string;
  senderRole: 'user' | 'assistant';
  createdAt: Date | string; // Can be string when coming from tRPC/JSON
  metadata?: {
    sources?: Array<{
      stepId: string;
      stepTitle: string;
      chunkIndex: number;
      score: number;
    }>;
    model?: string;
    processingTime?: number;
  };
}

interface OptimisticMessage extends Omit<Message, 'createdAt'> {
  status: 'server' | 'optimistic-user' | 'optimistic-ai-loading' | 'optimistic-ai-error';
  tempId?: string;
  createdAt: Date; // Always a Date object in processed messages
}

export function ChatInterface({ learningContentId, className }: ChatInterfaceProps) {
  const [message, setMessage] = useState('');
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Get or create conversation mutation
  const getOrCreateConversationMutation = api.chat.getOrCreateConversation.useMutation({
    onSuccess: (data) => {
      if (data.success) {
        setConversationId(data.data.id);
      }
    },
    onError: (error) => {
      console.error('Failed to get or create conversation:', error);
    },
  });

  // Initialize conversation when component mounts
  const initializeConversation = useCallback(() => {
    if (learningContentId && !conversationId) {
      getOrCreateConversationMutation.mutate({ learningContentId });
    }
  }, [learningContentId, conversationId, getOrCreateConversationMutation]);

  useEffect(() => {
    initializeConversation();
  }, [initializeConversation]);

  // Get conversation history
  const { data: historyData, isLoading: isLoadingHistory, refetch: refetchHistory, error: historyError } = api.chat.getConversationHistory.useQuery(
    { conversationId: conversationId || '' },
    {
      enabled: !!conversationId,
      retry: 3, // Limit retries to prevent infinite loops
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    }
  );

  // Send message mutation
  const sendMessageMutation = api.chat.sendMessage.useMutation({
    onSuccess: () => {
      // Clear optimistic messages and refetch to get server messages
      setOptimisticMessages([]);
      refetchHistory();
      // Focus input after sending
      setTimeout(() => inputRef.current?.focus(), 100);
    },
    onError: (error) => {
      console.error('Failed to send message:', error);
      // Handle error in handleSendMessage
    },
  });

  // Combine server messages with optimistic messages
  const allMessages: OptimisticMessage[] = useMemo(() => {
    const serverMessages: OptimisticMessage[] = historyData?.success
      ? historyData.data.messages.map((msg: Message) => ({
          ...msg,
          status: 'server' as const,
          // Ensure createdAt is a Date object (tRPC serializes dates as strings)
          createdAt: typeof msg.createdAt === 'string' ? new Date(msg.createdAt) : msg.createdAt,
        }))
      : [];

    return [...serverMessages, ...optimisticMessages].sort(
      (a, b) => {
        // Safely handle date comparison with fallback
        const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() : new Date(a.createdAt).getTime();
        const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() : new Date(b.createdAt).getTime();
        return aTime - bTime;
      }
    );
  }, [historyData, optimisticMessages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [allMessages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || !conversationId || sendMessageMutation.isPending) {
      return;
    }

    const userTempId = crypto.randomUUID();
    const aiTempId = crypto.randomUUID();
    const now = new Date();
    const messageContent = message.trim();

    // 1. Add optimistic user message immediately
    const optimisticUserMessage: OptimisticMessage = {
      id: userTempId,
      content: messageContent,
      senderRole: 'user',
      createdAt: now,
      status: 'optimistic-user',
      tempId: userTempId,
    };

    // 2. Add loading AI message
    const loadingAiMessage: OptimisticMessage = {
      id: aiTempId,
      content: '',
      senderRole: 'assistant',
      createdAt: new Date(now.getTime() + 1), // Slightly later timestamp
      status: 'optimistic-ai-loading',
      tempId: aiTempId,
    };

    // Add both messages to optimistic state
    setOptimisticMessages(prev => [...prev, optimisticUserMessage, loadingAiMessage]);
    setMessage(''); // Clear input immediately

    try {
      await sendMessageMutation.mutateAsync({
        conversationId,
        content: messageContent,
      });
      // Success is handled in mutation's onSuccess
    } catch {
      // Replace loading AI message with error message
      setOptimisticMessages(prev =>
        prev.map(msg =>
          msg.tempId === aiTempId
            ? {
                ...msg,
                status: 'optimistic-ai-error' as const,
                content: 'Failed to send message. Please try again.',
              }
            : msg
        )
      );
    }
  };

  const retryMessage = (aiTempId: string) => {
    // Find the user message that corresponds to this AI error
    const errorIndex = optimisticMessages.findIndex(msg => msg.tempId === aiTempId);
    if (errorIndex === -1) return;

    const userMessage = optimisticMessages[errorIndex - 1]; // User message should be right before AI message
    if (!userMessage || userMessage.senderRole !== 'user') return;

    // Remove the error message and add a new loading message
    const newAiTempId = crypto.randomUUID();
    const loadingAiMessage: OptimisticMessage = {
      id: newAiTempId,
      content: '',
      senderRole: 'assistant',
      createdAt: new Date(),
      status: 'optimistic-ai-loading',
      tempId: newAiTempId,
    };

    setOptimisticMessages(prev => [
      ...prev.filter(msg => msg.tempId !== aiTempId),
      loadingAiMessage,
    ]);

    // Retry the API call
    if (!conversationId) return;

    sendMessageMutation.mutateAsync({
      conversationId,
      content: userMessage.content,
    }).catch(() => {
      // If retry fails, show error again
      setOptimisticMessages(prev =>
        prev.map(msg =>
          msg.tempId === newAiTempId
            ? {
                ...msg,
                status: 'optimistic-ai-error' as const,
                content: 'Failed to send message. Please try again.',
              }
            : msg
        )
      );
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  if (getOrCreateConversationMutation.isPending) {
    return (
      <Card className={cn("h-full flex items-center justify-center", className)}>
        <CardContent className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-500 dark:text-gray-400">Setting up chat...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="flex-shrink-0 pb-3">
        <CardTitle className="flex items-center text-lg">
          <MessageSquare className="h-5 w-5 mr-2 text-blue-500" />
          Ask Questions
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Ask questions about this learning material and get AI-powered answers.
        </p>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col min-h-0 p-4">
        {/* Messages Container */}
        <div className="flex-1 overflow-y-auto mb-4 space-y-4 min-h-0">
          {isLoadingHistory ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">Loading conversation...</span>
            </div>
          ) : allMessages.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Bot className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Start a conversation
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 max-w-xs">
                Ask any question about the learning material you&apos;re studying. I&apos;ll help you understand it better!
              </p>
            </div>
          ) : (
            <>
              {allMessages.map((msg) => (
                <div
                  key={msg.id}
                  className={cn(
                    "flex gap-3 max-w-[85%]",
                    msg.senderRole === 'user' ? "ml-auto" : "mr-auto"
                  )}
                >
                  {/* Avatar */}
                  <div className={cn(
                    "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium",
                    msg.senderRole === 'user'
                      ? "bg-blue-500 order-2"
                      : "bg-green-500 order-1"
                  )}>
                    {msg.senderRole === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                  </div>

                  {/* Message Bubble */}
                  <div className={cn(
                    "flex-1 px-4 py-3 rounded-lg text-sm",
                    msg.senderRole === 'user'
                      ? "bg-blue-500 text-white order-1"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 order-2",
                    msg.status === 'optimistic-user' && "opacity-90"
                  )}>
                    {/* Loading state for AI */}
                    {msg.status === 'optimistic-ai-loading' && (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-gray-500 dark:text-gray-400">AI is thinking...</span>
                      </div>
                    )}

                    {/* Error state for AI */}
                    {msg.status === 'optimistic-ai-error' && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-red-500">
                          <AlertCircle className="h-4 w-4" />
                          <span>{msg.content}</span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => msg.tempId && retryMessage(msg.tempId)}
                          className="text-xs"
                        >
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Retry
                        </Button>
                      </div>
                    )}

                    {/* Normal message content */}
                    {(msg.status === 'server' || msg.status === 'optimistic-user') && (
                      <div className="whitespace-pre-wrap break-words">
                        {msg.content}
                      </div>
                    )}

                    {/* Source information for server AI messages */}
                    {msg.senderRole === 'assistant' && msg.status === 'server' && msg.metadata?.sources && msg.metadata.sources.length > 0 && (
                      <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                        <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Sources:</p>
                        <div className="space-y-1">
                          {msg.metadata.sources.slice(0, 3).map((source, index) => (
                            <div key={index} className="text-xs text-gray-600 dark:text-gray-300">
                              • {source.stepTitle}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}


            </>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Form */}
        <form onSubmit={handleSendMessage} className="flex-shrink-0">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ask a question about this learning material..."
              disabled={sendMessageMutation.isPending || !conversationId}
              className="flex-1"
              maxLength={2000}
            />
            <Button
              type="submit"
              disabled={!message.trim() || sendMessageMutation.isPending || !conversationId}
              size="icon"
              className="flex-shrink-0"
            >
              {sendMessageMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Error messages */}
          {sendMessageMutation.error && (
            <div className="mt-2 flex items-center text-sm text-red-600 dark:text-red-400">
              <AlertCircle className="h-4 w-4 mr-1" />
              Failed to send message. Please try again.
            </div>
          )}
          {historyError && (
            <div className="mt-2 flex items-center text-sm text-red-600 dark:text-red-400">
              <AlertCircle className="h-4 w-4 mr-1" />
              Failed to load conversation history. Please refresh the page.
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
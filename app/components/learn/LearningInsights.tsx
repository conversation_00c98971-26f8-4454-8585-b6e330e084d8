'use client';

import React from 'react';
import { TrendingUp, Brain, Clock, Target, AlertTriangle, CheckCircle, BarChart3, Lightbulb, ArrowRight } from 'lucide-react';
import { api } from '../../trpc';
import { cn } from '@learn-platform/shared-ui';
import Link from 'next/link';
import { DifficultyIndicator } from './DifficultyIndicator';

interface LearningInsightsProps {
  className?: string;
}

export function LearningInsights({ className = '' }: LearningInsightsProps) {
  const { data: insightsData, isLoading, error } = api.quiz.getLearningInsights.useQuery();

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col', className)}>
        <div className="animate-pulse flex-1">
          <div className="flex items-center mb-4">
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="ml-4 flex-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col', className)}>
        <div className="flex items-center mb-4">
          <BarChart3 className="h-8 w-8 text-gray-400" />
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Insights</h3>
            <p className="text-sm text-red-500">Unable to load insights</p>
          </div>
        </div>
      </div>
    );
  }

  const insights = insightsData?.insights;
  const dataPoints = insightsData?.dataPoints || 0;

  // No data state
  if (!insights || dataPoints === 0) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col', className)}>
        <div className="flex items-center mb-4">
          <BarChart3 className="h-8 w-8 text-purple-600" />
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Insights</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Personalized analytics</p>
          </div>
        </div>

        <div className="text-center py-8 flex-1 flex flex-col justify-center">
          <Brain className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
            Complete some quizzes to see your learning insights!
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500">
            We&#39;ll analyze your performance and provide personalized recommendations.
          </p>
        </div>
      </div>
    );
  }

  const { overallPerformance, questionTypeAnalysis, difficultyAnalysis, recommendations } = insights;

  // Get performance trend icon and color
  const getTrendInfo = (trend: string) => {
    switch (trend) {
      case 'improving':
        return { icon: TrendingUp, color: 'text-green-600', bg: 'bg-green-100 dark:bg-green-900' };
      case 'declining':
        return { icon: AlertTriangle, color: 'text-red-600', bg: 'bg-red-100 dark:bg-red-900' };
      default:
        return { icon: Target, color: 'text-blue-600', bg: 'bg-blue-100 dark:bg-blue-900' };
    }
  };

  const trendInfo = getTrendInfo(overallPerformance?.improvementTrend || 'stable');
  const TrendIcon = trendInfo.icon;

  // Get top struggling question type - check if questionTypeAnalysis exists and has items
  interface QuestionTypeAnalysisItem {
    type: string;
    averageScore: number;
    totalAttempted: number;
  }

  // Difficulty analysis item interface
  interface DifficultyAnalysisItem {
    difficulty: string;
    averageScore: number;
  }

  const strugglingType: QuestionTypeAnalysisItem | null = questionTypeAnalysis && questionTypeAnalysis.length > 0
    ? (questionTypeAnalysis
        .filter((type: QuestionTypeAnalysisItem) => type.averageScore < 70)
        .sort((a: QuestionTypeAnalysisItem, b: QuestionTypeAnalysisItem) => a.averageScore - b.averageScore)[0])
    : null;

  // Get recommendations - handle both array of strings and array of objects
  interface RecommendationObject {
    title: string;
    description?: string;
  }

  type Recommendation = string | RecommendationObject;

  const displayRecommendations: RecommendationObject[] = recommendations && (recommendations as Recommendation[]).length > 0
    ? (recommendations as Recommendation[]).slice(0, 2).map((rec: Recommendation, index: number): RecommendationObject => {
        if (typeof rec === 'string') {
          return { title: rec, description: '' };
        }
        return rec;
      })
    : [];

  return (
    <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
      {/* Header with Button in Top-Right */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <BarChart3 className="h-8 w-8 text-purple-600" />
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Insights</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Based on {dataPoints} quiz{dataPoints !== 1 ? 'es' : ''}
            </p>
          </div>
        </div>
        <Link href="/dashboard/insights">
          <button className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
            View Detailed Analytics →
          </button>
        </Link>
      </div>

      {/* 2x2 Grid Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Overall Performance Card */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white flex items-center">
              <TrendingUp className="h-4 w-4 text-purple-600 mr-2" />
              Overall Performance
            </h4>
            <div className={cn('flex items-center px-2 py-1 rounded-full text-xs font-medium', trendInfo.bg, trendInfo.color)}>
              <TrendIcon className="h-3 w-3 mr-1" />
              {overallPerformance?.improvementTrend || 'stable'}
            </div>
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <BarChart3 className="h-4 w-4 text-purple-600" />
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  {(overallPerformance?.averageScore || 0).toFixed(1)}%
                </div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Average Score</div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <Target className="h-4 w-4 text-blue-600" />
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  {Math.round(overallPerformance?.totalAttempts || 0)}
                </div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Total Attempts</div>
            </div>
          </div>
        </div>

        {/* Difficulty Analysis Card */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center mb-4">
            <Target className="h-4 w-4 text-blue-600 mr-2" />
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">Difficulty Performance</h4>
          </div>
          {difficultyAnalysis && difficultyAnalysis.length > 0 ? (
            <div className="space-y-2">
              {difficultyAnalysis.slice(0, 3).map((diff: DifficultyAnalysisItem, index: number) => (
              <div
                key={diff.difficulty}
                className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600"
              >
                <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <DifficultyIndicator
                  difficulty={diff.difficulty as 'easy' | 'medium' | 'hard'}
                  size="sm"
                  className="mr-2"
                  />
                  <span className="text-xs text-gray-600 dark:text-gray-300 capitalize">
                  {diff.difficulty}
                  </span>
                </div>
                <span className="text-sm font-bold text-gray-900 dark:text-white">
                  {diff.averageScore.toFixed(1)}%
                </span>
                </div>
              </div>
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600 text-center">
              <Clock className="h-4 w-4 text-gray-400 mx-auto mb-2" />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Complete more quizzes to see difficulty analysis
              </p>
            </div>
          )}
        </div>

        {/* Areas Needing Attention Card */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-4 w-4 text-orange-600 mr-2" />
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">Needs Attention</h4>
          </div>
          {strugglingType ? (
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 text-orange-600 mr-2" />
                  <span className="text-xs text-gray-600 dark:text-gray-300 capitalize">
                    {(strugglingType as QuestionTypeAnalysisItem)?.type || 'Unknown'} Questions
                  </span>
                </div>
                <span className="text-sm font-bold text-orange-600">
                  {(strugglingType as QuestionTypeAnalysisItem).averageScore.toFixed(1)}%
                </span>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {(strugglingType as QuestionTypeAnalysisItem).totalAttempted} attempts • Consider more practice
              </p>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600 text-center">
              <CheckCircle className="h-4 w-4 text-green-500 mx-auto mb-2" />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Great job! No areas need immediate attention.
              </p>
            </div>
          )}
        </div>

        {/* Recommendations Card */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center mb-4">
            <Lightbulb className="h-4 w-4 text-yellow-500 mr-2" />
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">Recommendations</h4>
          </div>
          {displayRecommendations.length > 0 ? (
            <div className="space-y-2">
              {displayRecommendations.slice(0, 2).map((rec, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                  <div className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-gray-600 dark:text-gray-300 leading-relaxed">
                      {rec.title}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600 text-center">
              <Lightbulb className="h-4 w-4 text-yellow-500 mx-auto mb-2" />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Keep up the great work! Complete more quizzes for personalized tips.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
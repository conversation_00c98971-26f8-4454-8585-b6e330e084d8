'use client';

import React from 'react';
import { Heart, Target, TrendingUp, Sparkles, BookOpen, ArrowRight, Plus } from 'lucide-react';
import Link from 'next/link';
import { api } from '../../trpc';
import {
  type UserInterestsResponse,
  type UserLearningInterest,
  type Topic,
  type Theme,
  type FocusArea,
} from '@learn-platform/shared-types';
import { useErrorToast } from '../../error-handling/error-toast';
import { ErrorBoundary } from '../../error-handling/error-boundary';
import { InterestsCardSkeleton } from '../ui/LoadingSkeleton';

interface InterestsCardProps {
  className?: string;
}

function InterestsCardContent({ className = '' }: InterestsCardProps) {
  const { handleError } = useErrorToast();

  const { data: interestsData, isLoading, error, refetch } = api.interests.getUserInterests.useQuery(
    {
      includeTopicDetails: true,
      includeThemeDetails: true,
      includeFocusAreaDetails: true,
    }
  );
  React.useEffect(() => {
    if (error) {
      handleError(error, { context: 'InterestsCard.getUserInterests' });
    }
  }, [error, handleError]);

  const { data: statsData, isLoading: statsLoading, error: statsError } = api.interests.getInterestStats.useQuery(undefined, {
    retry: (failureCount, error) => {
      if (error?.data?.code === 'UNAUTHORIZED') {
        return false;
      }
      return failureCount < 3;
    },
  });

  React.useEffect(() => {
    if (statsError) {
      handleError(statsError, { context: 'InterestsCard.getInterestStats' });
    }
  }, [statsError, handleError]);

  // Loading state
  if (isLoading || statsLoading) {
    return <InterestsCardSkeleton className={className} />;
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0">
            <Heart className="h-8 w-8 text-pink-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Interests</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Personalize your learning</p>
          </div>
        </div>

        <div className="text-center py-4 flex-1 flex flex-col justify-center">
          <div className="mb-4">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20">
              <Heart className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Unable to load interests
          </h4>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {error?.data?.code === 'UNAUTHORIZED'
              ? 'Please log in to view your learning interests.'
              : 'There was a problem loading your interests. Please try again.'
            }
          </p>
          <div className="space-y-2">
            <button
              onClick={() => refetch()}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-pink-600 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500"
            >
              Try Again
            </button>
            {error?.data?.code !== 'UNAUTHORIZED' && (
              <Link href="/dashboard/interests" className="block">
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500">
                  Set Up Interests
                </button>
              </Link>
            )}
          </div>
        </div>
      </div>
    );
  }

  const interests = interestsData?.interests || [];
  const stats = statsData?.stats;

  // No interests state
  if (interests.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0">
            <Heart className="h-8 w-8 text-pink-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Interests</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Personalize your learning</p>
          </div>
        </div>

        <div className="text-center py-4 flex-1 flex flex-col justify-center">
          <BookOpen className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            No learning interests set yet. Add your interests to get personalized recommendations!
          </p>
          <Link href="/dashboard/interests">
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-pink-600 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500">
              <Plus className="mr-2 h-4 w-4" />
              Add Interests
            </button>
          </Link>
        </div>
      </div>
    );
  }

  // Calculate display statistics
  const totalInterests = interests.length;
  const averageInterestLevel = stats?.averageInterestLevel || 0;
  const averagePriority = stats?.averagePriority || 0;
  const topFocusAreas = Object.entries(stats?.focusAreaDistribution || {})
    .sort(([,a], [,b]) => b - a)
    .slice(0, 2)
    .map(([name]) => name);

  // Get top interests for display
  interface TopInterestItem {
    interest: UserLearningInterest;
    topic?: Topic;
    theme?: Theme;
    focusArea?: FocusArea;
  }

  const topInterests: TopInterestItem[] = interests
    .sort(
      (a: TopInterestItem, b: TopInterestItem) =>
        b.interest.interestLevel * b.interest.priority -
        a.interest.interestLevel * a.interest.priority
    )
    .slice(0, 3);

  return (
    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Heart className="h-8 w-8 text-pink-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Interests</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Personalize your learning</p>
          </div>
        </div>
        <Link href="/dashboard/interests">
          <button className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
            Manage →
          </button>
        </Link>
      </div>

      {/* Statistics Grid */}
      <div className="grid grid-cols-3 gap-3 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Target className="h-4 w-4 text-pink-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{totalInterests}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Interests</p>
        </div>

        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{averageInterestLevel.toFixed(1)}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Avg Level</p>
        </div>

        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Sparkles className="h-4 w-4 text-purple-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{averagePriority.toFixed(1)}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Avg Priority</p>
        </div>
      </div>

      {/* Top Interests */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Top Interests</h4>
        <div className="space-y-2">
          {topInterests.map((item, index) => (
            <div key={item.interest.id} className="flex items-center justify-between text-sm">
              <div className="flex items-center min-w-0 flex-1">
                <span className="text-gray-600 dark:text-gray-400 mr-2">#{index + 1}</span>
                <span className="text-gray-900 dark:text-white truncate">
                  {item.topic?.name || 'Unknown Topic'}
                </span>
              </div>
              <div className="flex items-center space-x-2 ml-2">
                <div className="flex items-center">
                  <Heart className="h-3 w-3 text-pink-500 mr-1" />
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {item.interest.interestLevel}
                  </span>
                </div>
                <div className="flex items-center">
                  <Target className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {item.interest.priority}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Focus Areas */}
      {topFocusAreas.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Primary Focus</h4>
          <div className="flex flex-wrap gap-1">
            {topFocusAreas.map((area) => (
              <span
                key={area}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {area}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="mt-auto pt-4 space-y-2">
        <Link href="/dashboard/interests" className="block">
          <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-pink-600 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500">
            <Plus className="mr-2 h-4 w-4" />
            Manage Interests
          </button>
        </Link>

        <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500">
          <Sparkles className="mr-2 h-4 w-4" />
          Get Recommendations
        </button>
      </div>
    </div>
  );
}

// Main component with error boundary
export function InterestsCard(props: InterestsCardProps) {
  return (
    <ErrorBoundary
      fallback={
        <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${props.className || ''}`}>
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0">
              <Heart className="h-8 w-8 text-pink-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Interests</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Personalize your learning</p>
            </div>
          </div>

          <div className="text-center py-4 flex-1 flex flex-col justify-center">
            <div className="mb-4">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20">
                <Heart className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Something went wrong
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              We encountered an unexpected error while loading your interests.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-pink-600 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500"
            >
              Reload Page
            </button>
          </div>
        </div>
      }
    >
      <InterestsCardContent {...props} />
    </ErrorBoundary>
  );
}
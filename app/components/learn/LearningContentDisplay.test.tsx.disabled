import { render, screen, fireEvent } from '@testing-library/react';
import { LearningContentDisplay } from './LearningContentDisplay';
import { StepConfig } from '../templates/types';
import { Brain, BookOpen } from 'lucide-react';

// Mock the API
jest.mock('../../trpc', () => ({
  api: {
    useUtils: jest.fn(() => ({
      learningProgress: {
        getProgress: {
          invalidate: jest.fn()
        }
      }
    })),
    learningProgress: {
      getProgress: {
        useQuery: jest.fn(() => ({ data: null }))
      },
      addBookmark: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false
        }))
      },
      removeBookmark: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false
        }))
      },
      addNote: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false
        }))
      },
      updateNote: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false
        }))
      },
      deleteNote: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false
        }))
      },
      updateProgress: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false
        }))
      }
    },
    quiz: {
      getHistoryByLearningContent: {
        useQuery: jest.fn(() => ({
          data: {
            attempts: [
              {
                id: '1',
                quizId: 'quiz-1',
                quizTitle: 'Test Quiz',
                startedAt: '2024-01-01T00:00:00Z',
                completedAt: '2024-01-01T00:30:00Z',
                isCompleted: true,
                score: { percentage: 85, totalPoints: 100, earnedPoints: 85, correctAnswers: 17, totalQuestions: 20 },
                totalTimeSpent: 1800,
                difficulty: 'medium' as const
              },
              {
                id: '2',
                quizId: 'quiz-2',
                quizTitle: 'Another Quiz',
                startedAt: '2024-01-02T00:00:00Z',
                completedAt: '2024-01-02T00:25:00Z',
                isCompleted: true,
                score: { percentage: 92, totalPoints: 100, earnedPoints: 92, correctAnswers: 18, totalQuestions: 20 },
                totalTimeSpent: 1500,
                difficulty: 'easy' as const
              }
            ],
            stats: {
              totalAttempts: 2,
              completedAttempts: 2,
              averageScore: 88.5,
              averageTimeSpent: 1650,
              bestScore: 92,
              lastAttemptDate: '2024-01-02T00:00:00Z'
            }
          }
        }))
      }
    }
  }
}));

const mockSteps: StepConfig[] = [
  {
    title: "Step 1",
    icon: <Brain className="w-8 h-8 text-blue-500" />,
    type: 'paragraph',
    data: "This is step 1 content"
  },
  {
    title: "Step 2",
    icon: <BookOpen className="w-8 h-8 text-green-500" />,
    type: 'paragraph',
    data: "This is step 2 content"
  }
];

describe('LearningContentDisplay', () => {
  const defaultProps = {
    steps: mockSteps,
    contentId: 'test-content-id',
    onQuizSelect: jest.fn(),
    onRetakeQuiz: jest.fn(),
    onGenerateNew: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the component with quiz history popover trigger', () => {
    render(<LearningContentDisplay {...defaultProps} />);

    // Should show the quiz stats in the trigger button
    expect(screen.getByText('92%')).toBeInTheDocument(); // Best score
    expect(screen.getByText('2 quizzes')).toBeInTheDocument(); // Completed attempts
  });

  it('should open popover when clicking the quiz stats button', async () => {
    render(<LearningContentDisplay {...defaultProps} />);

    // Find and click the popover trigger
    const triggerButton = screen.getByRole('button', { name: /92% 2 quizzes/i });
    fireEvent.click(triggerButton);

    // Should show the popover content
    expect(screen.getByText('Quiz History')).toBeInTheDocument();
    expect(screen.getByText('2 attempts')).toBeInTheDocument();
  });

  it('should display quiz history items in the popover', async () => {
    render(<LearningContentDisplay {...defaultProps} />);

    // Open the popover
    const triggerButton = screen.getByRole('button', { name: /92% 2 quizzes/i });
    fireEvent.click(triggerButton);

    // Should show individual quiz attempts
    expect(screen.getByText('Attempt #2')).toBeInTheDocument();
    expect(screen.getByText('Attempt #1')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument(); // First quiz score
    expect(screen.getByText('92%')).toBeInTheDocument(); // Second quiz score
  });

  it('should call onQuizSelect when review button is clicked', async () => {
    render(<LearningContentDisplay {...defaultProps} />);

    // Open the popover
    const triggerButton = screen.getByRole('button', { name: /92% 2 quizzes/i });
    fireEvent.click(triggerButton);

    // Click review button for first quiz
    const reviewButtons = screen.getAllByTitle('Review quiz');
    fireEvent.click(reviewButtons[0]);

    expect(defaultProps.onQuizSelect).toHaveBeenCalledWith('quiz-2', '2');
  });

  it('should call onRetakeQuiz when retake button is clicked', async () => {
    render(<LearningContentDisplay {...defaultProps} />);

    // Open the popover
    const triggerButton = screen.getByRole('button', { name: /92% 2 quizzes/i });
    fireEvent.click(triggerButton);

    // Click retake button for first quiz
    const retakeButtons = screen.getAllByTitle('Retake quiz');
    fireEvent.click(retakeButtons[0]);

    expect(defaultProps.onRetakeQuiz).toHaveBeenCalledWith('quiz-2');
  });

  it('should show generate quiz button when no quiz history exists', () => {
    // Mock empty quiz history
    const { api } = require('../../trpc');
    api.quiz.getHistoryByLearningContent.useQuery.mockReturnValue({
      data: {
        attempts: [],
        stats: {
          totalAttempts: 0,
          completedAttempts: 0,
          averageScore: 0,
          averageTimeSpent: 0,
          bestScore: 0,
          lastAttemptDate: undefined
        }
      }
    });

    render(<LearningContentDisplay {...defaultProps} />);

    expect(screen.getByText('Generate Quiz')).toBeInTheDocument();
  });
});
'use client';

import { api } from '../../trpc';
import { Book<PERSON><PERSON>, Clock, Users, TrendingUp, FileText, Eye, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@learn-platform/shared-ui';

interface LearningCardProps {
  className?: string;
}

export function LearningCard({ className = '' }: LearningCardProps) {
  const { data: learningData, isLoading, error } = api.learningContent.getAll.useQuery({
    limit: 100, // Get more data for better statistics
    offset: 0,
  });

  // Loading state
  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="animate-pulse flex-1">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
            <div className="ml-4 flex-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-32"></div>
            </div>
          </div>
          <div className="mt-4 space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center flex-1">
          <div className="flex-shrink-0">
            <BookOpen className="h-8 w-8 text-gray-400" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Content</h3>
            <p className="text-sm text-red-500">Unable to load learning content</p>
          </div>
        </div>
      </div>
    );
  }

  const content = learningData?.content || [];

  // No data state (no learning content available)
  if (content.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0">
            <BookOpen className="h-8 w-8 text-purple-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Content</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Create and manage content</p>
          </div>
        </div>
        
        <div className="text-center py-4 flex-1 flex flex-col justify-center">
          <FileText className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            No learning content yet. Create your first content to get started!
          </p>
          <Link href="/dashboard/learn">
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
              Create Content
              <ArrowRight className="ml-2 h-4 w-4" />
            </button>
          </Link>
        </div>
      </div>
    );
  }

  // Calculate statistics
  const totalContent = content.length;
  const publicContent = content.filter((item: { isPublic: boolean }) => item.isPublic).length;
  const privateContent = totalContent - publicContent;
  const avgReadingTime = Math.round(
    content.reduce((sum: number, item: { estimatedReadingTime?: number }) => sum + (item.estimatedReadingTime || 0), 0) / totalContent
  );
  
  // Calculate difficulty distribution
  const difficultyStats = {
    beginner: content.filter((item: { learningLevel: string }) => item.learningLevel === 'beginner').length,
    intermediate: content.filter((item: { learningLevel: string }) => item.learningLevel === 'intermediate').length,
    advanced: content.filter((item: { learningLevel: string }) => item.learningLevel === 'advanced').length,
  };
  
  const mostCommonLevel = Object.entries(difficultyStats).reduce((a, b) => 
    difficultyStats[a[0] as keyof typeof difficultyStats] > difficultyStats[b[0] as keyof typeof difficultyStats] ? a : b
  )[0];

  return (
    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <BookOpen className="h-8 w-8 text-purple-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Content</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Create and manage content</p>
          </div>
        </div>
        <Link href="/dashboard/my-learning">
          <button className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
            View All →
          </button>
        </Link>
      </div>

      {/* Content Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <FileText className="h-4 w-4 text-purple-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{totalContent}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Total Content</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Eye className="h-4 w-4 text-green-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{publicContent}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Public</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Clock className="h-4 w-4 text-blue-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{avgReadingTime}m</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Avg Reading</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <TrendingUp className="h-4 w-4 text-orange-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white capitalize">{mostCommonLevel}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Most Common</p>
        </div>
      </div>

      {/* Content Visibility Distribution */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
          <span>Content Visibility</span>
        </div>
        <div className="flex space-x-1">
          {publicContent > 0 && (
            <div 
              className="bg-green-500 h-2 rounded-l-full" 
              style={{ width: `${(publicContent / totalContent) * 100}%` }}
              title={`Public: ${publicContent} content`}
            ></div>
          )}
          {privateContent > 0 && (
            <div 
              className="bg-purple-500 h-2 rounded-r-full" 
              style={{ width: `${(privateContent / totalContent) * 100}%` }}
              title={`Private: ${privateContent} content`}
            ></div>
          )}
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
            Public ({publicContent})
          </span>
          <span className="flex items-center">
            <div className="w-2 h-2 bg-purple-500 rounded-full mr-1"></div>
            Private ({privateContent})
          </span>
        </div>
      </div>

      {/* Recent Activity */}
      {totalContent > 0 && (
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
            {totalContent} content item{totalContent !== 1 ? 's' : ''} available
          </span>
        </div>
      )}
    </div>
  );
}
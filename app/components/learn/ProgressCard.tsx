'use client';

import React from 'react';
import { TrendingUp, Clock, Target, Trophy, BookOpen, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { api } from '../../trpc';

interface ProgressCardProps {
  className?: string;
}

export function ProgressCard({ className = '' }: ProgressCardProps) {
  const { data: statsData, isLoading, error } = api.learningProgress.getStats.useQuery();

  // Loading state
  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="animate-pulse flex-1">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
            <div className="ml-4 flex-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-32"></div>
            </div>
          </div>
          <div className="mt-4 space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center flex-1">
          <div className="flex-shrink-0">
            <TrendingUp className="h-8 w-8 text-gray-400" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Progress</h3>
            <p className="text-sm text-red-500">Unable to load progress data</p>
          </div>
        </div>
      </div>
    );
  }

  const stats = statsData?.stats;

  // No data state (user hasn't started learning yet)
  if (!stats || stats.totalContent === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0">
            <TrendingUp className="h-8 w-8 text-purple-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Progress</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Start your learning journey</p>
          </div>
        </div>
        
        <div className="text-center py-4 flex-1 flex flex-col justify-center">
          <BookOpen className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            No learning content yet. Create your first learning content to start tracking progress!
          </p>
          <Link href="/dashboard/learn">
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </button>
          </Link>
        </div>
      </div>
    );
  }

  // Format time spent (convert seconds to hours/minutes)
  const formatTimeSpent = (seconds: number): string => {
    if (seconds < 60) return `${Math.floor(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <TrendingUp className="h-8 w-8 text-purple-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Progress</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Track your learning journey</p>
          </div>
        </div>
        <Link href="/dashboard/progress">
          <button className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
            View Details →
          </button>
        </Link>
      </div>

      {/* Progress Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <BookOpen className="h-4 w-4 text-blue-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalContent}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Total Content</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Trophy className="h-4 w-4 text-green-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{stats.completedContent}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Completed</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Target className="h-4 w-4 text-orange-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{stats.averageCompletion.toFixed(1)}%</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Avg Progress</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Clock className="h-4 w-4 text-purple-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{formatTimeSpent(stats.totalTimeSpent)}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Time Spent</p>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-1">
          <span>Overall Progress</span>
          <span>{stats.averageCompletion.toFixed(1)}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${stats.averageCompletion.toFixed(1)}%` }}
          ></div>
        </div>
      </div>

      {/* Quick Stats */}
      {stats.inProgressContent > 0 && (
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
            {stats.inProgressContent} in progress
          </span>
        </div>
      )}
    </div>
  );
}
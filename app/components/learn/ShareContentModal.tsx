'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { 
  X, 
  Copy, 
  Check, 
  Globe, 
  Lock, 
  Twitter, 
  Facebook, 
  Linkedin, 
  Mail,
  Download,
  QrCode
} from 'lucide-react';

interface ShareContentModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: {
    id: string;
    title: string;
    description: string;
    isPublic: boolean;
  };
  onTogglePublic?: (isPublic: boolean) => void;
}

export function ShareContentModal({ isOpen, onClose, content, onTogglePublic }: ShareContentModalProps) {
  const [copied, setCopied] = useState(false);
  const [isTogglingPublic, setIsTogglingPublic] = useState(false);

  if (!isOpen) return null;

  const shareUrl = `${window.location.origin}/dashboard/learn/${content.id}`;
  const publicShareUrl = content.isPublic ? shareUrl : '';

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleTogglePublic = async () => {
    if (!onTogglePublic) return;
    
    setIsTogglingPublic(true);
    try {
      await onTogglePublic(!content.isPublic);
    } catch (error) {
      console.error('Failed to toggle public status:', error);
    } finally {
      setIsTogglingPublic(false);
    }
  };

  const handleSocialShare = (platform: string) => {
    const text = `Check out this learning content: ${content.title}`;
    const url = encodeURIComponent(shareUrl);
    const encodedText = encodeURIComponent(text);

    let shareUrl_platform = '';
    
    switch (platform) {
      case 'twitter':
        shareUrl_platform = `https://twitter.com/intent/tweet?text=${encodedText}&url=${url}`;
        break;
      case 'facebook':
        shareUrl_platform = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case 'linkedin':
        shareUrl_platform = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
        break;
      case 'email':
        shareUrl_platform = `mailto:?subject=${encodeURIComponent(content.title)}&body=${encodedText}%0A%0A${url}`;
        break;
    }

    if (shareUrl_platform) {
      window.open(shareUrl_platform, '_blank', 'width=600,height=400');
    }
  };

  const handleExportPDF = () => {
    // TODO: Implement PDF export functionality
    alert('PDF export functionality will be implemented in a future update');
  };

  const handleGenerateQR = () => {
    // TODO: Implement QR code generation
    alert('QR code generation will be implemented in a future update');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Share Learning Content</h2>
          <button
            onClick={onClose}
            className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Content Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 dark:text-white mb-1">{content.title}</h3>
            {content.description && (
              <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">{content.description}</p>
            )}
          </div>

          {/* Privacy Toggle */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 dark:text-white">Privacy Settings</h4>
            <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="flex items-center space-x-3">
                {content.isPublic ? (
                  <Globe className="h-5 w-5 text-green-600 dark:text-green-400" />
                ) : (
                  <Lock className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                )}
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {content.isPublic ? 'Public' : 'Private'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {content.isPublic 
                      ? 'Anyone with the link can view this content'
                      : 'Only you can view this content'
                    }
                  </p>
                </div>
              </div>
              {onTogglePublic && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTogglePublic}
                  disabled={isTogglingPublic}
                >
                  {isTogglingPublic ? 'Updating...' : (content.isPublic ? 'Make Private' : 'Make Public')}
                </Button>
              )}
            </div>
          </div>

          {/* Share Link */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 dark:text-white">Share Link</h4>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm text-gray-900 dark:text-white"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyLink}
                className="flex items-center space-x-1"
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4 text-green-600" />
                    <span>Copied</span>
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4" />
                    <span>Copy</span>
                  </>
                )}
              </Button>
            </div>
            {!content.isPublic && (
              <p className="text-xs text-amber-600 dark:text-amber-400">
                ⚠️ This content is private. Recipients will need to be logged in and have access to view it.
              </p>
            )}
          </div>

          {/* Social Sharing */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 dark:text-white">Share on Social Media</h4>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSocialShare('twitter')}
                className="flex items-center justify-center space-x-2"
              >
                <Twitter className="h-4 w-4" />
                <span>Twitter</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSocialShare('facebook')}
                className="flex items-center justify-center space-x-2"
              >
                <Facebook className="h-4 w-4" />
                <span>Facebook</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSocialShare('linkedin')}
                className="flex items-center justify-center space-x-2"
              >
                <Linkedin className="h-4 w-4" />
                <span>LinkedIn</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSocialShare('email')}
                className="flex items-center justify-center space-x-2"
              >
                <Mail className="h-4 w-4" />
                <span>Email</span>
              </Button>
            </div>
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 dark:text-white">Additional Options</h4>
            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportPDF}
                className="w-full flex items-center justify-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Export as PDF</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateQR}
                className="w-full flex items-center justify-center space-x-2"
              >
                <QrCode className="h-4 w-4" />
                <span>Generate QR Code</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
name: 'Setup Environment'
description: 'Setup Bun and install dependencies for the monorepo'
inputs:
  working-directory:
    description: 'Working directory for the setup'
    required: false
    default: '.'
runs:
  using: 'composite'
  steps:
    - name: Setup Bun
      uses: oven-sh/setup-bun@v2
      with:
        bun-version: latest

    - name: Cache Bun dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.bun/install/cache
          ${{ inputs.working-directory }}/node_modules
        key: ${{ runner.os }}-bun-${{ hashFiles(format('{0}/**/bun.lockb', inputs.working-directory)) }}
        restore-keys: |
          ${{ runner.os }}-bun-

    - name: Install dependencies
      shell: bash
      run: bun install --frozen-lockfile
      working-directory: ${{ inputs.working-directory }}

    - name: <PERSON>ache NX
      uses: actions/cache@v4
      with:
        path: |
          ${{ inputs.working-directory }}/.nx/cache
          ${{ inputs.working-directory }}/node_modules/.cache/nx
        key: ${{ runner.os }}-nx-${{ hashFiles(format('{0}/**/bun.lockb', inputs.working-directory)) }}-${{ hashFiles(format('{0}/nx.json', inputs.working-directory)) }}
        restore-keys: |
          ${{ runner.os }}-nx-
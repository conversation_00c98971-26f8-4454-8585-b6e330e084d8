name: Deploy Web App to Railway

on:
  push:
    branches:
      - main
      - dev
    paths:
      - 'learn-platform/apps/web/**'
      - 'learn-platform/libs/**'
      - 'learn-platform/package.json'
      - 'learn-platform/bun.lock'
      - 'learn-platform/nx.json'
      - 'learn-platform/tsconfig.base.json'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'main'
        type: choice
        options:
          - main
          - dev

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  test:
    runs-on: ubuntu-latest
    name: Test Web App
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup environment
        uses: ./.github/actions/setup
        with:
          working-directory: learn-platform

      - name: Lint web app
        run: bunx nx lint web
        working-directory: learn-platform

      - name: Test affected libraries
        run: bunx nx affected --target=test --parallel=3 --coverage
        working-directory: learn-platform

      - name: Build web app
        run: bunx nx build web
        working-directory: learn-platform

      - name: Upload test coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: web-test-coverage
          path: coverage/
          retention-days: 7

  deploy:
    runs-on: ubuntu-latest
    name: Deploy Web to Railway
    needs: test
    if: always() && needs.test.result == 'success'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup environment
        uses: ./.github/actions/setup
        with:
          working-directory: learn-platform

      - name: Set environment variables
        working-directory: learn-platform
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "DEPLOY_ENVIRONMENT=${{ github.event.inputs.environment }}" >> $GITHUB_ENV
          else
            echo "DEPLOY_ENVIRONMENT=$(git branch --show-current)" >> $GITHUB_ENV
          fi

      - name: Get Railway token
        run: |
          env_var="RAILWAY_TOKEN__WEB_$(echo $DEPLOY_ENVIRONMENT | tr '[:lower:]' '[:upper:]')"
          echo "RAILWAY_TOKEN_VAR=$env_var" >> $GITHUB_ENV

      - name: Install Railway CLI
        run: |
          RAILWAY_BINARY="/tmp/railway"
          RAILWAY_VERSION="3.0.22"
          VERSION="$RAILWAY_VERSION" INSTALL_DIR="$RAILWAY_BINARY" sh -c "$(curl -sSL https://raw.githubusercontent.com/railwayapp/cli/master/install.sh)"
          echo "/tmp" >> $GITHUB_PATH

      - name: Deploy to Railway
        working-directory: learn-platform
        run: |
          cd apps/web
          railway_token="${{ secrets[env.RAILWAY_TOKEN_VAR] }}"
          if [[ "$railway_token" == "" ]]; then
            echo "❌ No Railway token found for web app in environment: $DEPLOY_ENVIRONMENT"
            echo "Expected secret: $RAILWAY_TOKEN_VAR"
            exit 1
          fi
          echo "🚀 Deploying web app to Railway environment: $DEPLOY_ENVIRONMENT"
          RAILWAY_TOKEN="$railway_token" railway up --detach --verbose

      - name: Deployment success
        run: |
          echo "✅ Web app deployed successfully to Railway!"
          echo "Environment: $DEPLOY_ENVIRONMENT"
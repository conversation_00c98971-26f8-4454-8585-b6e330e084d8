name: Deploy Affected Projects to Railway

on:
  push:
    branches:
      - main
      - dev
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'main'
        type: choice
        options:
          - main
          - dev
      force_deploy:
        description: 'Force deploy web project for testing (ignore affected detection)'
        required: false
        default: true
        type: boolean



jobs:
  test:
    runs-on: ubuntu-latest
    name: Test Affected Projects
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup environment
        uses: ./.github/actions/setup
        with:
          working-directory: learn-platform

      - name: Set base commit hash
        run: |
          #!/usr/bin/env bash
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # For manual triggers, compare against the target branch
            echo "BASE_COMMIT=origin/${{ github.event.inputs.environment || 'main' }}" >> $GITHUB_ENV
          else
            # For push events, use the previous commit
            commit="${{ github.event.before }}"
            if git branch --contains "$commit" 2>/dev/null; then
              echo "No force push detected, using previous commit"
              echo "BASE_COMMIT=$commit" >> $GITHUB_ENV
            else
              # Handle force push or initial commit
              commit=$(git log --format="%H" -n 2 | tail -n 1)
              echo "Force push detected, using: $commit"
              echo "BASE_COMMIT=$commit" >> $GITHUB_ENV
            fi
          fi

      - name: Display base commit
        run: echo "Base commit:" $BASE_COMMIT

      - name: Lint affected projects
        working-directory: learn-platform
        run: bunx nx affected --target=lint --parallel=3 --base="$BASE_COMMIT" --head=HEAD

      - name: Test affected projects
        working-directory: learn-platform
        run: bunx nx affected --target=test --parallel=3 --base="$BASE_COMMIT" --head=HEAD --coverage

      - name: Build affected projects
        working-directory: learn-platform
        env:
          NODE_ENV: production
        run: bunx nx affected --target=build --parallel=3 --base="$BASE_COMMIT" --head=HEAD

      - name: Upload test coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-coverage
          path: coverage/
          retention-days: 7

  deploy:
    runs-on: ubuntu-latest
    container: ghcr.io/railwayapp/cli:latest
    name: Deploy to Railway
    needs: test
    if: always() && needs.test.result == 'success'
    steps:
      - name: Install git for checkout
        run: |
          apk update
          apk add --no-cache git

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js and Bun
        run: |
          # Install additional dependencies (git already installed)
          apk add --no-cache nodejs npm curl bash unzip gcompat
          
          # Install Bun using musl binary for Alpine Linux compatibility
          mkdir -p $HOME/.bun/bin
          curl -fsSL https://github.com/oven-sh/bun/releases/latest/download/bun-linux-x64-musl.zip -o /tmp/bun.zip
          unzip /tmp/bun.zip -d /tmp/
          mv /tmp/bun-linux-x64-musl/bun $HOME/.bun/bin/bun
          chmod +x $HOME/.bun/bin/bun
          
          # Create bunx symlink
          ln -sf $HOME/.bun/bin/bun $HOME/.bun/bin/bunx
          
          # Add Bun to PATH for current and future steps
          echo "$HOME/.bun/bin" >> $GITHUB_PATH
          export PATH="$HOME/.bun/bin:$PATH"
          
          # Verify Bun installation
          $HOME/.bun/bin/bun --version
          ls -la $HOME/.bun/bin/

      - name: Install dependencies
        working-directory: learn-platform
        run: |
          # Use full path to bun for installation
          $HOME/.bun/bin/bun install

      - name: Setup git and fetch references
        run: |
          # Configure git to avoid issues with safe directory
          # Use the actual GitHub Actions workspace path
          git config --global --add safe.directory $GITHUB_WORKSPACE
          git config --global --add safe.directory /__w/kwaci-learning/kwaci-learning
          # Also add wildcard as fallback for any workspace path
          git config --global --add safe.directory '*'
          
          # Fetch all remote references to ensure we have access to origin/main
          git fetch origin
          
          # Ensure we have the main branch reference
          git branch -r

      - name: Set base commit hash
        working-directory: learn-platform
        run: |
          #!/usr/bin/env bash
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # For manual triggers, compare against the target branch
            target_branch="${{ github.event.inputs.environment || 'main' }}"
            echo "BASE_COMMIT=origin/$target_branch" >> $GITHUB_ENV
            echo "DEPLOY_ENVIRONMENT=$target_branch" >> $GITHUB_ENV
            echo "FORCE_DEPLOY=${{ github.event.inputs.force_deploy || 'false' }}" >> $GITHUB_ENV
          else
            # For push events, use the previous commit and set force deploy to true for testing
            commit=${{ github.event.before }}
            echo "FORCE_DEPLOY=true" >> $GITHUB_ENV
            if git branch --contains "$commit" 2>/dev/null; then
              echo "No force push detected, using previous commit"
              echo "BASE_COMMIT=$commit" >> $GITHUB_ENV
            else
              # Handle force push or initial commit - use origin/main as fallback
              if git rev-parse --verify origin/main >/dev/null 2>&1; then
                echo "Using origin/main as base for comparison"
                echo "BASE_COMMIT=origin/main" >> $GITHUB_ENV
              else
                # Last resort: use the previous commit from git log
                commit=$(git log --format="%H" -n 2 | tail -n 1)
                echo "Force push detected, using: $commit"
                echo "BASE_COMMIT=$commit" >> $GITHUB_ENV
              fi
            fi
            echo "DEPLOY_ENVIRONMENT=" >> $GITHUB_ENV
          fi

      - name: Display deployment info
        run: |
          echo "Base commit: $BASE_COMMIT"
          echo "Deploy environment: $DEPLOY_ENVIRONMENT"
          echo "Force deploy: $FORCE_DEPLOY"

      - name: Check affected projects
        working-directory: learn-platform
        run: |
          echo "Affected projects:"
          # Use full path to bunx to ensure it's found
          AFFECTED_PROJECTS=$($HOME/.bun/bin/bunx nx show projects --affected --base="$BASE_COMMIT" --head=HEAD --type=app)
          echo "$AFFECTED_PROJECTS"
          echo "AFFECTED_PROJECTS<<EOF" >> $GITHUB_ENV
          echo "$AFFECTED_PROJECTS" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Deploy affected projects
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        run: |
          # Check if force deploy is enabled
          if [ "$FORCE_DEPLOY" = "true" ]; then
            echo "Force deploy enabled - deploying web project"
            AFFECTED_PROJECTS="web"
          elif [ -z "$AFFECTED_PROJECTS" ]; then
            echo "No affected projects to deploy"
            exit 0
          fi
          
          for project in $AFFECTED_PROJECTS; do
            echo "Deploying project: $project"
            
            # Determine Railway service ID based on project name
            case $project in
              "web")
                SERVICE_ID="${{ secrets.RAILWAY_SERVICE_ID_WEB }}"
                ;;
              "api")
                SERVICE_ID="${{ secrets.RAILWAY_SERVICE_ID_API }}"
                ;;
              "admin")
                SERVICE_ID="${{ secrets.RAILWAY_SERVICE_ID_ADMIN }}"
                ;;
              *)
                echo "Unknown project: $project, skipping..."
                continue
                ;;
            esac
            
            if [ -z "$SERVICE_ID" ]; then
              echo "No Railway service ID configured for project: $project"
              continue
            fi
            
            echo "Deploying $project to Railway service: $SERVICE_ID"
            # Set the app name environment variable for nixpacks
            export NIXPACKS_NX_APP_NAME=$project
            # Deploy from the learn-platform directory (monorepo context)
            cd learn-platform
            railway up --service="$SERVICE_ID"
            cd ..
          done



  notify:
    runs-on: ubuntu-latest
    name: Notify Deployment Status
    needs: [test, deploy]
    if: always()
    steps:
      - name: Deployment Success
        if: needs.deploy.result == 'success'
        run: |
          echo "✅ Deployment completed successfully!"
          echo "All affected projects have been deployed to Railway."

      - name: Deployment Failed
        if: needs.deploy.result == 'failure'
        run: |
          echo "❌ Deployment failed!"
          echo "Check the deployment logs for more details."
          exit 1

      - name: Tests Failed
        if: needs.test.result == 'failure'
        run: |
          echo "❌ Tests failed!"
          echo "Deployment was skipped due to test failures."
          exit 1